-- 房源导入性能优化 - 数据库索引建议
-- =============================================

-- 1. 小区表(Estate)索引优化
-- 用于批量查询小区信息时的性能优化

-- 小区名称索引（如果不存在）
CREATE INDEX idx_estate_xqmc ON estate(xqmc);

-- 行政区划代码索引（如果不存在）
CREATE INDEX idx_estate_area_code ON estate(area_code);

-- 复合索引：行政区划代码 + 小区名称
-- 这是最重要的索引，用于房源导入时的小区查询
CREATE INDEX idx_estate_area_xqmc ON estate(area_code, xqmc);

-- 2. 楼栋表(Building)索引优化
-- 用于批量查询楼栋信息时的性能优化

-- 小区ID索引（如果不存在）
CREATE INDEX idx_building_xq_id ON building(xq_id);

-- 楼栋名称索引（如果不存在）
CREATE INDEX idx_building_ldmc ON building(ldmc);

-- 复合索引：小区ID + 楼栋名称
-- 这是最重要的索引，用于房源导入时的楼栋查询
CREATE INDEX idx_building_xq_ldmc ON building(xq_id, ldmc);

-- 3. 房源表(House)索引优化
-- 用于房源数据插入和查询优化

-- 小区ID索引（如果不存在）
CREATE INDEX idx_house_xq_id ON house(xq_id);

-- 楼栋ID索引（如果不存在）
CREATE INDEX idx_house_ld_id ON house(ld_id);

-- 房屋唯一性检查复合索引
-- 用于验证房屋是否已存在（HouseValidate::isExist）
CREATE INDEX idx_house_unique_check ON house(xq_id, ld_id, dy, lc, xh);

-- 房屋状态索引（用于查询统计）
CREATE INDEX idx_house_fwzt ON house(fwzt);

-- 房屋性质索引（用于查询统计）
CREATE INDEX idx_house_fwxz ON house(fwxz);

-- 4. 字典相关表索引优化
-- 用于字典数据查询优化

-- 字典类型表索引
CREATE INDEX idx_dict_type_dict_type ON dict_type(dict_type);
CREATE INDEX idx_dict_type_status ON dict_type(status);

-- 字典数据表索引
CREATE INDEX idx_dict_data_type_id ON dict_data(dict_type_id);
CREATE INDEX idx_dict_data_status ON dict_data(status);
CREATE INDEX idx_dict_data_label ON dict_data(label);
CREATE INDEX idx_dict_data_value ON dict_data(value);

-- 复合索引：字典类型ID + 状态 + 排序
CREATE INDEX idx_dict_data_type_status_sort ON dict_data(dict_type_id, status, sort);

-- 5. 软删除相关索引
-- 用于软删除查询优化

-- 小区表软删除索引
CREATE INDEX idx_estate_delete_time ON estate(delete_time);

-- 楼栋表软删除索引
CREATE INDEX idx_building_delete_time ON building(delete_time);

-- 房源表软删除索引
CREATE INDEX idx_house_delete_time ON house(delete_time);

-- 字典表软删除索引
CREATE INDEX idx_dict_type_delete_time ON dict_type(delete_time);
CREATE INDEX idx_dict_data_delete_time ON dict_data(delete_time);

-- 6. 时间戳索引
-- 用于按时间查询和统计

-- 创建时间索引
CREATE INDEX idx_house_create_time ON house(create_time);
CREATE INDEX idx_estate_create_time ON estate(create_time);
CREATE INDEX idx_building_create_time ON building(create_time);

-- 更新时间索引
CREATE INDEX idx_house_update_time ON house(update_time);
CREATE INDEX idx_estate_update_time ON estate(update_time);
CREATE INDEX idx_building_update_time ON building(update_time);

-- 7. 数据权限相关索引
-- 用于数据权限范围查询优化（ManageAreaScopeTrait）

-- 行政区划代码索引（如果表中有该字段）
CREATE INDEX idx_house_area_code ON house(area_code);
CREATE INDEX idx_building_area_code ON building(area_code);

-- 8. 性能监控查询
-- 用于检查索引使用情况

-- 查看表的索引信息
-- SHOW INDEX FROM estate;
-- SHOW INDEX FROM building;
-- SHOW INDEX FROM house;
-- SHOW INDEX FROM dict_type;
-- SHOW INDEX FROM dict_data;

-- 9. 索引维护建议

-- 定期分析表统计信息
-- ANALYZE TABLE estate, building, house, dict_type, dict_data;

-- 检查索引碎片
-- OPTIMIZE TABLE estate, building, house, dict_type, dict_data;

-- 10. 查询优化验证

-- 验证小区查询性能
-- EXPLAIN SELECT id, area_code, xqmc FROM estate 
-- WHERE area_code IN ('code1', 'code2') AND xqmc IN ('name1', 'name2');

-- 验证楼栋查询性能
-- EXPLAIN SELECT id, xq_id, ldmc FROM building 
-- WHERE xq_id IN (1, 2, 3) AND ldmc IN ('building1', 'building2');

-- 验证房屋唯一性检查性能
-- EXPLAIN SELECT id FROM house 
-- WHERE xq_id = 1 AND ld_id = 1 AND dy = '单元1' AND lc = 1 AND xh = 1;

-- 11. 注意事项
-- ================

-- 1. 索引会占用额外的存储空间
-- 2. 索引会影响INSERT/UPDATE/DELETE的性能
-- 3. 需要根据实际查询模式调整索引策略
-- 4. 定期监控索引使用情况，删除不必要的索引
-- 5. 对于大表，建议在业务低峰期创建索引

-- 12. 监控SQL示例
-- ================

-- 查看慢查询
-- SELECT * FROM mysql.slow_log WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 DAY);

-- 查看索引使用统计
-- SELECT * FROM performance_schema.table_io_waits_summary_by_index_usage 
-- WHERE object_schema = 'your_database_name';

-- 查看表扫描统计
-- SELECT * FROM performance_schema.table_io_waits_summary_by_table 
-- WHERE object_schema = 'your_database_name' 
-- ORDER BY sum_timer_wait DESC;
