CREATE TABLE `tenant` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `apply_id` int(11) NOT NULL COMMENT '申报ID',
  `archive_id` int(11) DEFAULT NULL COMMENT '档案ID',
  `czrzjhm` varchar(32) COLLATE utf8_unicode_ci NOT NULL COMMENT '主承租人证件号码',
  `name` varchar(50) COLLATE utf8_unicode_ci NOT NULL COMMENT '姓名',
  `zjlb` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '证件类别:1身份证,99其他',
  `zjhm` varchar(32) COLLATE utf8_unicode_ci NOT NULL COMMENT '证件号码',
  `birth` date DEFAULT NULL COMMENT '出生日期',
  `sex` tinyint(1) NOT NULL DEFAULT '1' COMMENT '性别:0未知,1男,2女',
  `mz` tinyint(1) DEFAULT NULL COMMENT '民族',
  `marriage` tinyint(1) NOT NULL COMMENT '婚姻状况:1未婚,2已婚,3丧偶,4离异,99其他',
  `lxdh` varchar(16) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `jjlxrdh` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '紧急联系人电话',
  `sfzj_file` varchar(512) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '证件影像文件路径',
  `yzsqrgx` tinyint(1) NOT NULL COMMENT '与主申请人关系:1申请人,2配偶,...,99其他',
  `is_gtsqr` tinyint(1) NOT NULL COMMENT '是否共同申请人:0否,1是',
  `hjszd` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '户籍所在地',
  `hjszdxq` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '户籍所在地详情',
  `is_bdhj` tinyint(1) DEFAULT NULL COMMENT '是否本地户籍:0否,1是',
  `hjqr_date` date DEFAULT NULL COMMENT '户籍迁入日期',
  `xjzszd` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '现居住所在地',
  `jznx` int(11) DEFAULT NULL COMMENT '居住年限',
  `jzzhm` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '居住证号码',
  `jzzfzjg` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '居住证发证机关',
  `jyqk` tinyint(1) DEFAULT NULL COMMENT '就业情况:1在职,...,5灵活就业,99其他',
  `gzdw` varchar(128) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '工作单位',
  `dwdz` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '单位地址',
  `dwdh` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '单位电话',
  `zylx` tinyint(2) DEFAULT NULL COMMENT '职业类型',
  `gzxz` tinyint(1) DEFAULT NULL COMMENT '工作性质:1行政在编,...,3企业职工',
  `bzqd_date` date DEFAULT NULL COMMENT '编制取得日期',
  `jynx` int(11) DEFAULT NULL COMMENT '就业年限',
  `ygxz` tinyint(1) DEFAULT NULL COMMENT '用工性质:1普通合同制,2专技人才',
  `zcmc` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '职称名称',
  `ht_start_date` date DEFAULT NULL COMMENT '劳动合同开始日期',
  `ht_end_date` date DEFAULT NULL COMMENT '劳动合同结束日期',
  `ysr` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '月收入',
  `nsr` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '年收入',
  `qt_nsr` decimal(8,2) DEFAULT NULL COMMENT '其他年收入(工薪外)',
  `jtnsr` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '家庭年收入(可支配)',
  `rjnsr` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '人均年收入(可支配)',
  `yhkh` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '银行卡号',
  `txj` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '退休金养老金',
  `gjj` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '公积金',
  `ck` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '存款',
  `qcgj` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '汽车估价',
  `sybx` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '商业保险',
  `qtcc` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '其他财产',
  `fcjz` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '房产价值',
  `jysr` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '经营净收入',
  `syf` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '赡养费',
  `fyf` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '抚养费',
  `fyf_1` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '扶养费',
  `whcd` tinyint(1) DEFAULT NULL COMMENT '文化程度:1小学,...,8博士后,99其他',
  `by_date` date DEFAULT NULL COMMENT '毕业日期',
  `sb_start_date` date DEFAULT NULL COMMENT '社保缴纳开始日期',
  `sb_end_date` date DEFAULT NULL COMMENT '社保缴纳结束日期',
  `is_dbry` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '是否低保人员',
  `dbzhm` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '低保证号码',
  `ssqt` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '所属特殊群体',
  `is_poor_support` tinyint(1) DEFAULT '0' COMMENT '是否特困供养：0否，1是',
  `is_veteran` tinyint(1) DEFAULT '0' COMMENT '是否退役军人：0否，1是',
  `is_fire_rescue` tinyint(1) DEFAULT '0' COMMENT '是否消防救援人员：0否，1是',
  `is_heroic` tinyint(1) DEFAULT '0' COMMENT '是否见义勇为：0否，1是',
  `is_sanitation_worker` tinyint(1) DEFAULT '0' COMMENT '是否环卫职工：0否，1是',
  `is_bus_worker` tinyint(1) DEFAULT '0' COMMENT '是否公交职工：0否，1是',
  `is_teacher` tinyint(1) DEFAULT '0' COMMENT '是否教育工作者：0否，1是',
  `is_medical_worker` tinyint(1) DEFAULT '0' COMMENT '是否医护人员：0否，1是',
  `is_elderly` tinyint(1) DEFAULT '0' COMMENT '是否老年人：0否，1是',
  `is_disabled_person` tinyint(1) DEFAULT '0' COMMENT '是否残疾人：0否，1是',
  `is_special_family` tinyint(1) DEFAULT '0' COMMENT '是否计生特殊家庭：0否，1是',
  `is_orphan` tinyint(1) DEFAULT '0' COMMENT '是否成年孤儿：0否，1是',
  `is_rural_migrant` tinyint(1) DEFAULT '0' COMMENT '是否农业转移人口：0否，1是',
  `child_count` tinyint(2) DEFAULT '0' COMMENT '子女数量：0无，1单孩，2二孩，3三孩+',
  `is_serious_illness` tinyint(1) DEFAULT '0' COMMENT '是否患大病：0否，1是',
  `is_disaster_victim` tinyint(1) DEFAULT '0' COMMENT '是否受灾家庭：0否，1是',
  `is_yf` tinyint(1) DEFAULT NULL COMMENT '是否有房:0无房,1有房',
  `yfxzmj` float(8,2) DEFAULT NULL COMMENT '有房现住面积(㎡)',
  `yfrjmj` float(8,2) DEFAULT NULL COMMENT '有房人均面积(㎡)',
  `cq_state` tinyint(1) DEFAULT '0' COMMENT '产权状态:0无产权,1有产权',
  `cq_syr` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '登记的权利人',
  `cq_htbh` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '登记编号或合同备案号',
  `cq_fwzl` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '房屋坐落栋单元房号',
  `cq_yt` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '用途',
  `cqcx_time` datetime DEFAULT NULL COMMENT '产权查询时间',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(11) DEFAULT NULL COMMENT '删除时间',
  `admin_id` int(11) DEFAULT NULL COMMENT '操作管理员ID',
  `area_code` varchar(16) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '行政区划编码',
  `bz` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='家庭成员信息表';
CREATE TABLE `family_member` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `apply_id` int(11) NOT NULL COMMENT '申报id',
  `archive_id` int(11) NOT NULL COMMENT '档案id',
  `czrzjhm` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '主承租人证件号码',
  `yzsqrgx` tinyint(1) NOT NULL COMMENT '与主申请人关系:1申请人,2配偶,3子女,4父母,5岳父母(公婆),6祖父母(外祖父母),7媳婿,8孙子女(外孙子女),9兄弟姐妹,99其他',
  `is_gtsqr` tinyint(1) NOT NULL COMMENT '是否共同申请人:0否,1是',
  `name` varchar(50) COLLATE utf8_unicode_ci NOT NULL COMMENT '姓名',
  `zjlb` tinyint(1) NOT NULL COMMENT '证件类别:1身份证,99其他',
  `zjhm` varchar(18) COLLATE utf8_unicode_ci NOT NULL COMMENT '证件号码',
  `birth` date DEFAULT NULL COMMENT '出生日期',
  `sex` tinyint(1) NOT NULL DEFAULT '1' COMMENT '性别:0未知,1男,2女',
  `mz` tinyint(1) DEFAULT NULL COMMENT '民族',
  `marriage` tinyint(1) NOT NULL COMMENT '婚姻:1未婚,2已婚,3丧偶,4离异,99其他',
  `hjszd` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '户籍所在地',
  `hjszdxq` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '户籍所在地详情',
  `xjzszd` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '现居住所在地',
  `is_bdhj` tinyint(1) DEFAULT NULL COMMENT '是否本地户籍:0否,1是',
  `jznx` int(11) DEFAULT NULL COMMENT '居住年限',
  `hjqr_date` date DEFAULT NULL COMMENT '户籍迁入日期',
  `whcd` tinyint(1) DEFAULT NULL COMMENT '文化程度:1小学,2初中,3高中,4大专,5本科,6硕士,7博士,8博士后,99其他',
  `by_date` date DEFAULT NULL COMMENT '毕业日期',
  `jyqk` tinyint(1) DEFAULT NULL COMMENT '就业情况:1在职,2待业,3失业,4离退休,5灵活就业,99其他',
  `zylx` tinyint(2) DEFAULT NULL COMMENT '职业',
  `gzdw` varchar(128) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '工作单位',
  `gzxz` tinyint(1) DEFAULT NULL COMMENT '工作性质:1行政在编人员,2行政聘用人员,3企业职工',
  `dwdh` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '单位电话',
  `dwdz` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '单位地址',
  `bzqd_date` date DEFAULT NULL COMMENT '编制取得日期',
  `jynx` int(11) DEFAULT NULL COMMENT '就业年限',
  `ygxz` tinyint(1) DEFAULT NULL COMMENT '用工性质:1普通合同制,2专技人才',
  `zcmc` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '职称名称',
  `ht_start_date` date DEFAULT NULL COMMENT '合同开始日期',
  `ht_end_date` date DEFAULT NULL COMMENT '合同结束日期',
  `ysr` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '月收入',
  `nsr` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '年收入',
  `qt_nsr` decimal(8,2) DEFAULT NULL COMMENT '其他年收入-工薪外',
  `jtnsr` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '家庭年收入-可支配',
  `rjnsr` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '人均年收入-可支配',
  `sb_start_date` date DEFAULT NULL COMMENT '社保缴纳开始日期',
  `sb_end_date` date DEFAULT NULL COMMENT '社保缴纳结束日期',
  `lxdh` varchar(16) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `jjlxrdh` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '紧急联系人电话',
  `ssqt` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '所属群体',
  `is_poor_support` tinyint(1) DEFAULT '0' COMMENT '是否特困供养：0否，1是',
  `is_veteran` tinyint(1) DEFAULT '0' COMMENT '是否退役军人：0否，1是',
  `is_fire_rescue` tinyint(1) DEFAULT '0' COMMENT '是否消防救援人员：0否，1是',
  `is_heroic` tinyint(1) DEFAULT '0' COMMENT '是否见义勇为：0否，1是',
  `is_sanitation_worker` tinyint(1) DEFAULT '0' COMMENT '是否环卫职工：0否，1是',
  `is_bus_worker` tinyint(1) DEFAULT '0' COMMENT '是否公交职工：0否，1是',
  `is_teacher` tinyint(1) DEFAULT '0' COMMENT '是否教育工作者：0否，1是',
  `is_medical_worker` tinyint(1) DEFAULT '0' COMMENT '是否医护人员：0否，1是',
  `is_elderly` tinyint(1) DEFAULT '0' COMMENT '是否老年人：0否，1是',
  `is_disabled_person` tinyint(1) DEFAULT '0' COMMENT '是否残疾人：0否，1是',
  `is_special_family` tinyint(1) DEFAULT '0' COMMENT '是否计生特殊家庭：0否，1是',
  `is_orphan` tinyint(1) DEFAULT '0' COMMENT '是否成年孤儿：0否，1是',
  `is_rural_migrant` tinyint(1) DEFAULT '0' COMMENT '是否农业转移人口：0否，1是',
  `child_count` tinyint(2) DEFAULT '0' COMMENT '子女数量：0无，1单孩，2二孩，3三孩+',
  `is_serious_illness` tinyint(1) DEFAULT '0' COMMENT '是否患大病：0否，1是',
  `is_disaster_victim` tinyint(1) DEFAULT '0' COMMENT '是否受灾家庭：0否，1是',
  `jzzhm` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '居住证号码',
  `jzzfzjg` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '居住证发证机关',
  `yhkh` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '银行卡号',
  `txj` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '退休金养老金',
  `gjj` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '公积金',
  `ck` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '存款',
  `qcgj` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '汽车估价',
  `sybx` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '商业保险',
  `qtcc` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '其他财产',
  `fcjz` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '房产价值',
  `jysr` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '经营净收入',
  `syf` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '赡养费',
  `fyf` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '抚养费',
  `fyf_1` decimal(8,2) unsigned DEFAULT '0.00' COMMENT '扶养费',
  `is_dbry` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '是否低保人员',
  `dbzhm` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '低保证号码',
  `bz` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '备注',
  `cq_state` tinyint(1) DEFAULT '0' COMMENT '产权状态:0:无产权,1有产权',
  `cq_syr` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '登记的权利人',
  `cq_htbh` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '登记编号或合同备案号',
  `cq_fwzl` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '房屋坐落栋单元房号',
  `cq_yt` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '用途',
  `cqcx_time` datetime DEFAULT NULL COMMENT '产权查询日期',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(11) DEFAULT NULL COMMENT '删除时间',
  `sfzj_file` varchar(512) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '证件影像文件',
  `is_yf` tinyint(1) DEFAULT NULL COMMENT '是否有房:0无房,1有房',
  `yfxzmj` float(8,2) DEFAULT NULL COMMENT '有房现住面积',
  `yfrjmj` float(8,2) DEFAULT NULL COMMENT '有房人均面积',
  `qy_id` int(11) DEFAULT NULL COMMENT '所属管理区域ID',
  `admin_id` int(11) DEFAULT NULL COMMENT '管理员ID',
  `area_code` varchar(16) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '行政区划',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6345 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='业务-家庭成员';
