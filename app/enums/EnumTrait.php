<?php

namespace app\enums;

trait EnumTrait
{
    public static function getCode(string $name): mixed
    {
        foreach (static::cases() as $enum) {
            if ($enum->name === $name) {
                return $enum->value;
            }
        }
        return null;
    }

    public static function getName(int $code): ?string
    {
        foreach (static::cases() as $enum) {
            if ($enum->value === $code) {
                return $enum->name;
            }
        }
        return null;
    }

    public static function codeOf($codeOrName, $default = null): mixed
    {
        foreach (static::cases() as $enum) {
            if ($enum->value === $codeOrName || $enum->name === $codeOrName) {
                //                return $enum;
                return $enum->value;
            }
        }
        return $default !== null ? $default : static::cases()[0];
    }

    public static function getEnumValues(): array
    {
        return array_map(fn($case) => $case->value, static::cases());
    }

    public static function getValueByLabel(string $label): string|int|null
    {
        foreach (self::cases() as $case) {
            if ($case->label() === $label) {
                return $case->value;
            }
        }
        return null;
    }

    public static function all(): array
    {
        return static::cases();
    }

    public static function getLabelByValue(string|int $Value)
    {
        foreach (static::cases() as $case) {
            if ($case->value === $Value) {
                return $case->label();
            }
        }
        return null;
    }
}
