<?php

namespace app\enums\ration;

use app\enums\EnumTrait;
use app\enums\IEnum;

enum LeaseTypeEnum: int implements IEnum
{
    use EnumTrait;

    case XCYZ = 1; // 现场摇中配租
    case LHAX = 2; // 轮候按序配租
    case LHGB = 3; // 轮候改报配租
    case QT = 4; // 其它：附情况说明及相关证明支持
    case LZG = 5; // 廉转公
    case GZL = 6; // 公转廉
    case TF = 7; // 调房入住
    case DX = 8; // 定向配租
    case NS = 9; // 年审配租
    case OTHER = 99; // 其它

    public function label(): string
    {
        return match ($this) {
            self::XCYZ => '现场摇中配租',
            self::LHAX => '轮候按序配租',
            self::LHGB => '轮候改报配租',
            self::QT => '其它：附情况说明及相关证明支持',
            self::LZG => '廉转公',
            self::GZL => '公转廉',
            self::TF => '调房入住',
            self::DX => '定向配租',
            self::NS => '年审配租',
            self::OTHER => '其他',
        };
    }
}
