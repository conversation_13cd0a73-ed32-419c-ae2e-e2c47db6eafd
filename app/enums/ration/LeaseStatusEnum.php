<?php

namespace app\enums\ration;

use app\enums\EnumTrait;
use app\enums\IEnum;

enum LeaseStatusEnum: int implements IEnum
{
    use EnumTrait;

    case CANCELLED = -1;
    case INVALIDATED = 0;
    case PENDING = 10;
    case SIGNED = 20;
    case CHECKED_IN = 30;
    case PENDING_REVIEW = 40;
    case REVIEW = 41;
    case RENEWED = 45;
    case CHANGE = 50;
    case CHECKOUT = 60;
    case CHECKED_OUT = 70;

    public function label(): string
    {
        return match ($this) {
            self::CANCELLED => '已作废',
            self::INVALIDATED => '已失效',
            self::PENDING => '待签约',
            self::SIGNED => '已签约',
            self::CHECKED_IN => '已入住',
            self::PENDING_REVIEW => '待年审',
            self::REVIEW => '年审中',
            self::RENEWED => '已年审',
            self::CHANGE => '调房中',
            self::CHECKOUT => '退房中',
            self::CHECKED_OUT => '已退房',
        };
    }
}
