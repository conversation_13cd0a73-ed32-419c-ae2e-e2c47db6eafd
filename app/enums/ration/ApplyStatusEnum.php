<?php

namespace app\enums\ration;

use app\enums\EnumTrait;
use app\enums\IEnum;

enum ApplyStatusEnum: int implements IEnum
{
    use EnumTrait;

    case DRAFT = 0;
    case TEMP = 100;
    case RETURNED = 105;
    case SUBMITTED = 110;
    case FINISHED = 320;

    public function label(): string
    {
        return match ($this) {
            self::DRAFT => '草稿',
            self::TEMP => '暂存',
            self::RETURNED => '退回',
            self::SUBMITTED => '审核中',
            self::FINISHED => '已通过',
        };
    }
}
