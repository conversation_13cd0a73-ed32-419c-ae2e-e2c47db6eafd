<?php

namespace app\enums\ration;

use app\enums\EnumTrait;
use app\enums\IEnum;

enum AttachmentTypeEnum: string implements IEnum
{
    use EnumTrait;

    case ID_CARD = 'id_card';
    case TEMP_ID_CARD = 'temp_id_card';
    case HOUSEHOLD = 'household';
    case PASSPORT = 'passport';
    case MARRIAGE = 'marriage';
    case DIVORCE = 'divorce';
    case DISABILITY = 'disability';
    case SOCIAL_SECURITY = 'social_security';
    case INCOME_PROOF = 'income_proof';

    public function label(): string
    {
        return match ($this) {
            self::ID_CARD => '身份证',
            self::TEMP_ID_CARD => '临时身份证',
            self::HOUSEHOLD => '户口本',
            self::PASSPORT => '护照',
            self::MARRIAGE => '结婚证',
            self::DIVORCE => '离婚证',
            self::DISABILITY => '残疾证',
            self::SOCIAL_SECURITY => '社保卡',
            self::INCOME_PROOF => '收入证明',
        };
    }
}
