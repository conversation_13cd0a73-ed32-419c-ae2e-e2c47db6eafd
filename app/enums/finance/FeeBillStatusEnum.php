<?php

namespace app\enums\finance;

use app\enums\EnumTrait;
use app\enums\IEnum;

enum FeeBillStatusEnum: int implements IEnum
{
    use EnumTrait;

    case INVALIDATED = -1;   // 已作废
    case DRAFT = 0;          // 草稿
    case ISSUED = 1;         // 已出账
    case PARTIAL_PAID = 2;   // 部分支付
    case PAID = 3;           // 已支付
    case WRITE_OFF = 4;       // 已冲销

    public function label(): string
    {
        return match ($this) {
            self::INVALIDATED => '已作废',
            self::DRAFT => '未出账',
            self::ISSUED => '已出账',
            self::PARTIAL_PAID => '部分支付',
            self::PAID => '已支付',
            self::WRITE_OFF => '已冲销',
        };
    }
}
