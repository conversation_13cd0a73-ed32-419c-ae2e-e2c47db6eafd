<?php

namespace app\service;

use app\controller\backend\model\ration\Archive;
use app\controller\backend\model\ration\Contract;
use app\controller\backend\model\ration\Lease;
use app\enums\finance\FeeBillStatusEnum;
use app\enums\finance\FeeDetailStatusEnum;
use app\enums\ration\ArchiveStatusEnum;
use app\enums\ration\ContractStatusEnum;
use app\enums\ration\LeaseStatusEnum;
use app\model\finance\FeeBill;
use app\model\finance\FeeDetail;
use app\model\ration\TenantCheckoutRecord;
use Carbon\Carbon;
use think\facade\Db;
use think\facade\Log;
use tools\Random;

class FinanceService
{
    /**
     * 支付完成处理
     *
     * @param int $bill_id 账单ID
     * @throws \Exception
     */
    public function paymentCompleted($bill_id): void
    {
        Db::startTrans();
        try {

            $bill = FeeBill::findOrEmpty($bill_id);
            if ($bill->isEmpty()) {
                throw new \Exception('账单不存在2');
            }

            if ($bill->status !== FeeBillStatusEnum::ISSUED->value) {
                throw new \Exception('账单状态不是未支付');
            }

            // 账单明细
            $billDetailsResult = FeeDetail::where('bill_id', $bill->id)
                ->where('status', FeeBillStatusEnum::ISSUED->value)
                ->select();

            if (!$billDetailsResult) {
                throw new \Exception('账单明细不存在');
            }
            $billDetailsResult = $billDetailsResult->toArray();
            $item_keys = array_unique(array_column($billDetailsResult, 'fee_item_key'));
            if (empty($item_keys)) {
                throw new \Exception('账单明细费用项不存在');
            }

            $bill->status = FeeBillStatusEnum::PAID->value;
            $bill->pay_time = date('Y-m-d H:i:s');
            $bill->pay_method = 'offline';
            $bill->save();

            $this->handleRentPayment($bill);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            throw new \Exception('支付失败2: ' . $e->getMessage());
        }
    }

    /**
     * 处理租金支付逻辑
     *
     * @param FeeBill $bill
     * @throws \Exception
     */
    private function handleRentPayment(FeeBill $bill): void
    {
        // 这里可以添加租金支付完成后的逻辑
        // 首次支付租金时，可能需要更新合同状态、档案状态等
        $lease = Lease::findOrEmpty($bill->lease_id);
        if ($lease->isEmpty()) {
            throw new \Exception('配租记录不存在');
        }
        $contract = Contract::findOrEmpty($bill->contract_id);
        if ($contract->isEmpty()) {
            throw new \Exception('合同不存在');
        }

        // todo 根据租约状态更执行后续逻辑
        if ($lease->status == LeaseStatusEnum::SIGNED->value) {
            if ($lease->type == 1) {
                $contract->status = ContractStatusEnum::IN_PROGRESS->value; // 进行中
                $contract->save();

                // 更新配租状态
                $lease->status = LeaseStatusEnum::CHECKED_IN->value; // 已入住
                $lease->save();
                Archive::update(['status' => ArchiveStatusEnum::CHECKED_IN], ['id' => $lease->archive_id]); // 更新档案状态为在住
            }
        }

        if ($lease->status == LeaseStatusEnum::CHECKOUT->value) {
            // todo 如果是退租结算 处理退租后的逻辑
        }
    }

    /**
     * 退租预计算服务
     *
     * @param int $contract_id 合同ID
     * @param string|null $checkout_date 退租日期（格式：Y-m-d）
     * @return array
     * @throws \Exception
     */
    public function preCalculateBeforeCheckout(int $contract_id, ?string $checkout_date = null): array
    {
        // 1. 参数校验
        // 2. 获取合同和租约数据
        // 3. 查询租金账单明细（已出账/已支付）
        // 4. 计算需补缴和可退金额
        // 5. 计算押金退还
        // 6. 返回计算结果
        try {
            $checkout_date = $checkout_date ?: date('Y-m-d');

            $contract = Contract::where('status', ContractStatusEnum::IN_PROGRESS->value)->findOrEmpty($contract_id);

            if ($contract->isEmpty()) {
                throw new \Exception('合同不存在或状态无效');
            }

            // 检查退租日期是否在合同有效期内
            if (Carbon::parse($checkout_date) < Carbon::parse($contract->ht_ksrq)) {
                throw new \Exception('退租日期不能早于合同开始日');
            }

            if (Carbon::parse($checkout_date) > Carbon::parse($contract->ht_jsrq)) {
                throw new \Exception('退租日期不能晚于合同结束日');
            }

            $rentDetails = FeeDetail::where([
                'archive_id' => $contract->archive_id,
                'lease_id' => $contract->lease_id,
                'contract_id' => $contract_id,
                'fee_item_key' => 'rent',
            ])
                ->whereIn('status', [
                    FeeDetailStatusEnum::ISSUED->value,
                    FeeDetailStatusEnum::PAID->value
                ])
                ->select();

            $checkoutDate = Carbon::parse($checkout_date);

            $payable_rent = 0;
            $refundable_rent = 0;
            $rent_fee_rule_id = 0;
            $rent_fee_item_id = 0;

            foreach ($rentDetails as $detail) {
                $periodStart = Carbon::parse($detail->period_stime);
                $periodEnd = Carbon::parse($detail->period_etime);
                $rent_fee_item_id = $detail->fee_item_id;
                $rent_fee_rule_id = $detail->fee_rule_id;

                if ($detail->status == FeeDetailStatusEnum::PAID->value) {
                    $refundable_rent += $this->calculateRefund($detail, $periodStart, $periodEnd, $checkoutDate);
                } else {
                    $payable_rent += $this->calculateArrears($detail, $periodStart, $periodEnd, $checkoutDate);
                }
            }

            $rentDeposit = FeeDetail::where(['archive_id' => $contract->archive_id, 'fee_item_key' => 'rent_deposit'])->find();
            $refundable_deposit = $rentDeposit ? $rentDeposit->amount : 0;

            $total = $refundable_rent + $refundable_deposit - $payable_rent;

            return [
                'checkout_date' => $checkout_date,
                'payable_rent' => $payable_rent,
                'refundable_rent' => $refundable_rent,
                'refundable_deposit' => $refundable_deposit,
                'total_amount' => round($total, 2),
                'deposit_fee_rule_id' => $rentDeposit->fee_rule_id ?? 0,
                'deposit_fee_item_id' => $rentDeposit->fee_item_id ?? 0,
                'rent_fee_rule_id' => $rent_fee_rule_id,
                'rent_fee_item_id' => $rent_fee_item_id,
            ];

        } catch (\Exception $e) {
            Log::error("退租计算失败 | {$contract_id}-{$contract->lease_id} | " . $e->getMessage());
            throw new \Exception("退租计算失败: " . $e->getMessage());
        }
    }

    /**
     * 计算应退租金
     */
    private function calculateRefund($detail, Carbon $periodStart, Carbon $periodEnd, Carbon $checkoutDate): float
    {
        if ($periodEnd <= $checkoutDate) {
            return 0;
        }

        $totalDays = $periodEnd->diffInDays($periodStart) + 1;

        // 退租日在周期内的情况
        if ($checkoutDate > $periodStart) {
            $usedDays = $checkoutDate->diffInDays($periodStart) + 1; // 实际入住天数

            if ($usedDays <= 15) {
                // 不足15天：退剩余天数金额（总天数-15天）
                $refundDays = $totalDays - 15;
                return $refundDays > 0
                    ? round($detail->amount * ($refundDays / $totalDays), 2)
                    : 0;
            } else {
                // 超过15天：不退
                return 0;
            }
        }

        return $detail->amount; // 整期可退
    }

    /**
     * 计算需补缴租金
     */
    private function calculateArrears($detail, Carbon $periodStart, Carbon $periodEnd, Carbon $checkoutDate): float
    {
        if ($checkoutDate < $periodStart) {
            return 0;
        }

        $totalDays = $periodEnd->diffInDays($periodStart) + 1;
        $usedDays = $checkoutDate->diffInDays($periodStart) + 1;

        // 短周期直接取账单金额（已按实际天数计算）
        if ($totalDays <= 15) {
            return $detail->amount;
        }

        // 长周期按15天规则
        if ($usedDays <= 15) {
            return round($detail->amount * (15 / $totalDays), 2);
        }

        return $detail->amount;
    }

    /**
     * 办理退房结算
     * @throws \Exception
     */
    public function checkoutRefund(array $data): array
    {
        Db::startTrans();
        try {
            // 1. 参数校验
            if (empty($data['contract_id']) || empty($data['checkout_date'])) {
                throw new \Exception('缺失必要参数');
            }

            // 2. 验证业务状态
            $contract = Contract::findOrEmpty($data['contract_id']);
            if ($contract->isEmpty() || $contract->status !== ContractStatusEnum::IN_PROGRESS->value) {
                throw new \Exception('合同状态无效');
            }

            $lease = Lease::findOrEmpty($contract->lease_id);
            if ($lease->isEmpty()) {
                throw new \Exception('配租记录不存在');
            }

            if ($lease->status !== LeaseStatusEnum::CHECKED_IN->value) {
                throw new \Exception('当前状态不能办理退房');
            }


            // 3. 重新验证计算数据（防止客户端篡改）
            $preCalculation = $this->preCalculateBeforeCheckout(
                $data['contract_id'],
                $data['checkout_date']
            );

            // 金额差异超过1元则报警
            if (abs($preCalculation['total_amount'] - $data['total_amount']) > 1) {
                Log::alert("退房金额异常", [
                    'calculated' => $preCalculation['total_amount'],
                    'submitted' => $data['total_amount']
                ]);
                throw new \Exception('结算金额校验失败，请重新计算');
            }

            // 4. 生成退房结算账单
            $checkoutBill = FeeBill::create([
                'contract_id' => $contract->id,
                'lease_id' => $contract->lease_id,
                'archive_id' => $contract->archive_id,
                'house_id' => $contract->fw_id,
                'bill_no' => 'TF' . date('YmdHis') . Random::build('numeric', 6),
                'bill_type' => $preCalculation['total_amount'] > 0 ? 2 : 3,
                'amount' => abs($preCalculation['total_amount']),
                'start_date' => $data['checkout_date'],
                'end_date' => $data['checkout_date'],
                'status' => FeeBillStatusEnum::ISSUED->value,
                'remark' => '退房结算-' . ($preCalculation['total_amount'] >= 0 ? '补缴' : '退款')
            ]);

            // 5. 记录结算明细
            $this->createCheckoutDetails($checkoutBill, $preCalculation, $data);

            // 6. 更新业务状态
            $lease->status = LeaseStatusEnum::CHECKOUT->value;
            $lease->save();

            // 7. 退房记录
            TenantCheckoutRecord::create([
                'archive_id' => $lease->archive_id,
                'lease_id' => $contract->lease_id,
                'contract_id' => $contract->id,
                'checkout_date' => $data['checkout_date'],
                'checkout_type' => $data['checkout_type'] ?? 1,
                'payable_rent' => $preCalculation['payable_rent'],
                'refundable_rent' => $preCalculation['refundable_rent'],
                'refundable_deposit' => $preCalculation['refundable_deposit'],
                'remark' => ''
            ]);

            Db::commit();

            return [
                'bill_id' => $checkoutBill->id,
                'bill_no' => $checkoutBill->bill_no,
                'amount' => $preCalculation['total_amount'],
                'checkout_date' => $data['checkout_date'],
            ];

        } catch (\Exception $e) {
            Db::rollback();
            Log::error("退房失败：" . $e->getMessage(), $data);
            throw new \Exception('退房办理失败：' . $e->getMessage());
        }
    }

    /**
     * 创建退房结算明细
     */
    private function createCheckoutDetails($bill, array $calculation, array $data): void
    {
        $temp = [];
        // 补缴明细
        if ($calculation['payable_rent'] > 0) {
            $temp[] = [
                'bill_id' => $bill->id,
                'contract_id' => $bill->contract_id,
                'lease_id' => $bill->lease_id,
                'archive_id' => $bill->archive_id,
                'house_id' => $bill->house_id,
                'fee_item_key' => 'rent',
                'fee_item_id' => $calculation['rent_fee_item_id'] ?? 0,
                'fee_rule_id' => $calculation['rent_fee_rule_id'] ?? 0,
                'amount' => $calculation['payable_rent'],
                'period_stime' => $data['checkout_date'] . ' 00:00:00',
                'period_etime' => $data['checkout_date'] . ' 23:59:59',
                'status' => FeeDetailStatusEnum::ISSUED->value,
                'remark' => '退房租金补缴'
            ];
        }

        // 退款明细
        if ($calculation['refundable_rent'] > 0) {
            $temp[] = [
                'bill_id' => $bill->id,
                'contract_id' => $bill->contract_id,
                'lease_id' => $bill->lease_id,
                'archive_id' => $bill->archive_id,
                'house_id' => $bill->house_id,
                'fee_item_key' => 'other',
                'fee_item_id' => 0,
                'fee_rule_id' => 0,
                'amount' => -$calculation['refundable_rent'], // 负数表示退款
                'period_stime' => $data['checkout_date'] . ' 00:00:00',
                'period_etime' => $data['checkout_date'] . ' 23:59:59',
                'status' => FeeDetailStatusEnum::ISSUED->value,
                'remark' => '退房租金退还'
            ];
        }

        // 押金退还
        if ($calculation['refundable_deposit'] > 0) {
            $temp[] = [
                'bill_id' => $bill->id,
                'contract_id' => $bill->contract_id,
                'lease_id' => $bill->lease_id,
                'archive_id' => $bill->archive_id,
                'house_id' => $bill->house_id,
                'fee_item_key' => 'rent_deposit',
                'fee_item_id' => $calculation['deposit_fee_item_id'] ?? 0,
                'fee_rule_id' => $calculation['deposit_fee_rule_id'] ?? 0,
                'amount' => -$calculation['refundable_deposit'],
                'period_stime' => $data['checkout_date'] . ' 00:00:00',
                'period_etime' => $data['checkout_date'] . ' 23:59:59',
                'status' => FeeDetailStatusEnum::ISSUED->value,
                'remark' => '押金退还'
            ];
        }

        if (!empty($temp)) {
            FeeDetail::insertAll($temp);
        }
    }

}
