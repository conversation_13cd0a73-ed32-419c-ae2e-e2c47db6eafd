<?php

namespace app\service\AllocationStrategy;

class StrategyFactory
{
    public static function createForRegion(string $regionCode)
    {
        return match ($regionCode) {
            '4203' => new WaitingTimeStrategy(),
            '420304', '420323', '420322', '420324', '420325', '420381' => new ScoreAndLotteryStrategy(),
            default => new CategoryLotteryStrategy()
        };
    }
}
