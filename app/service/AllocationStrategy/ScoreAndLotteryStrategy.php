<?php

namespace app\service\AllocationStrategy;

// 积分排序+摇号

class ScoreAndLotteryStrategy implements RationStrategyInterface
{
    public function allocate(array $applicants, array $houses): array
    {
        // 先按积分降序，同分组内随机排序
        usort($applicants, function($a, $b) {
            return $b['score'] <=> $a['score'];
        });
        $grouped = [];
        foreach ($applicants as $applicant) {
            $grouped[$applicant['score']][] = $applicant;
        }
        $finalList = [];
        foreach ($grouped as $group) {
            shuffle($group);
            $finalList = array_merge($finalList, $group);
        }
        // 分配房源
        return array_slice($finalList, 0, count($houses));
    }
}
