<?php

namespace app\service\AllocationStrategy;

class WaitingTimeStrategy implements RationStrategyInterface
{
    public function allocate(array $applicants, array $houses): array
    {
        // 按 waiting_time 升序排序，时间早的优先
        usort($applicants, function($a, $b) {
            return strtotime($a['waiting_time']) <=> strtotime($b['waiting_time']);
        });
        // 分配房源
        return array_slice($applicants, 0, count($houses));
    }
}
