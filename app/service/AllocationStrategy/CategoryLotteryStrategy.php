<?php

namespace app\service\AllocationStrategy;

// 分类队列+摇号
class CategoryLotteryStrategy implements RationStrategyInterface
{
    public function allocate(array $applicants, array $houses): array
    {
        $categories = [];
        foreach ($applicants as $applicant) {
            $categories[$applicant['category']][] = $applicant;
        }
        $result = [];
        foreach ($categories as &$group) {
            shuffle($group);
            $result = array_merge($result, $group);
        }
        return array_slice($result, 0, count($houses));
    }
}
