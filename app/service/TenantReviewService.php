<?php

namespace app\service;

use app\controller\backend\model\ration\Apply;
use app\controller\backend\model\ration\ContractRenewApply;
use app\controller\backend\model\ration\Lease;
use app\controller\backend\validate\ration\ApplyValidate;
use app\enums\ration\ContractStatusEnum;
use app\enums\ration\LeaseStatusEnum;
use app\model\ration\TenantChangeHistory;
use think\facade\Db;
use think\Model;

class TenantReviewService
{
    // 处理承租人年审逻辑
    // 1. 检查年审状态
    //    - 如果已年审，返回提示
    //    - 如果未年审，继续处理
    // 2. 提交年审材料
    // 3. 自动核验政务数据
    // 4. 异常件转人工审核
    // 5. 审核结果回填
    // 6. 推送年审结果给承租人
    // 7. 更新房源管理资格状态
    /**
     * @throws \Exception
     */
    public function handleTenantReview($data): Model|ContractRenewApply
    {
        $leaseId = $data['lease_id'] ?? null;

        Db::startTrans();
        try {
            //  验证数据
            validate(ApplyValidate::class)->scene('edit')->check($data);

            // 1. 检查年审状态
            $lease = Lease::findOrEmpty($leaseId);
            if ($lease->isEmpty()) {
                throw new \Exception('承租信息不存在');
            }
            if ($lease->status !== LeaseStatusEnum::PENDING_REVIEW->value) {
                throw new \Exception('承租信息状态不正确，无法进行年审');
            }
            if ($lease->contract?->status !== ContractStatusEnum::IN_PROGRESS->value) {
                throw new \Exception('合同状态不正确，无法进行年审');
            }

            // 获取申请信息
            $apply = Apply::where('id', $lease->archive->apply_id)->find();

            // 2. todo 提交年审材料 修改状态为待审核
            $lease->status = LeaseStatusEnum::REVIEW->value; // 设置为待审核状态
            $lease->save();

            // 3. 保存变更信息 核验政务数据
            (new TenantChangeHistory())->create([
                'apply_id' => $apply->id,
                'archive_id' => $lease->archive_id,
                'change_type' => TenantChangeHistory::TYPE_RENEW,
                'before_data' => $apply,
                'after_data' => $data,
                'add_user' => $data['add_user'] ?? '',
                'admin_id' => $data['admin_id'] ?? 0,
            ]);

            $result = ContractRenewApply::create([
                'archive_id' => $lease->archive_id,
                'lease_id' => $leaseId,
                'contract_id' => $lease->contract_id,
                'apply_time' => date('Y-m-d H:i:s'),
                'status' => 0,// 待审核状态
                'area_code' => $lease->area_code,
            ]);

            Db::commit();
            return $result;

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

}
