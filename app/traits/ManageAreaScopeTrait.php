<?php

namespace app\traits;

use think\db\Query;

trait ManageAreaScopeTrait
{
    use DataScopeTrait;

    /**
     * 应用管理区域的查询范围
     *
     * @param Query $query 查询构建器实例
     * @return Query
     */
    public function scopeManageArea($query): Query
    {
        $request = request();
        $request_route = $request->rule()?->getRoute();

        if (str_starts_with($request_route ?? '', 'backend')) {
            $conditions = $this->handleBackendDataPermissions($request);
        } elseif (str_starts_with($request_route ?? '', 'front')) {
            $conditions = $this->handleFrontDataPermissions($request);
        } else {
            return $query;
        }

        // 数据表字段
        $tableFields = $this->getTableFields();
        if (is_array($conditions)) {
            foreach ($conditions as $condition) {
                if (!is_array($condition) || count($condition) < 3) {
                    continue;
                }

                list($field, $operator, $value) = $condition;

                // 检查字段是否在数据表字段中
                if (in_array($field, $tableFields)) {
                    $query->where($field, $operator, $value);
                }
            }
        }

        return $query;
    }

    public function handleBackendDataPermissions($request): ?array
    {
        $auth = $request->x_token_user;
        if ($auth && $this->enableDataScope) {
            return $this->getDataScopeCondition($auth->id, $this->adminIdField ?? 'admin_id');
        }

        return null;
    }

    public function handleFrontDataPermissions($request): array
    {
        $auth_qy_id = $request->header('AuthQyId') ?? '4203';

        if ($auth_qy_id !== "4203" && str_starts_with($auth_qy_id, '4203')) {
            $areaCodes = $this->getSubAreas($auth_qy_id);
        } else {
            $areaCodes = ["4203"];
            $subAreas1 = $this->getSubAreas("420302");
            $subAreas2 = $this->getSubAreas("420303");
            $areaCodes = array_merge($areaCodes, $subAreas1, $subAreas2);
        }
        $condition[] = ['area_code', 'in', $areaCodes];

        return $condition;
    }

    public function generateAlias(): string
    {
        return basename(str_replace('\\', '/', get_class($this)));
    }

}
