<?php
declare (strict_types=1);

namespace app\listener;

use app\model\ration\TenantEventRecord;
use think\facade\Log;

class TenantEvent
{
    public function handle($event): void
    {
        try {
            if (is_array($event)) {
                // 支持批量写入
                if (isset($event[0]) && is_array($event[0])) {
                    TenantEventRecord::insertAll($event);
                } else {
                    TenantEventRecord::create($event);
                }
            } elseif ($event instanceof TenantEventRecord) {
                $event->save();
            } else {
                Log::warning('TenantEvent 监听器收到未知类型事件: ' . var_export($event, true));
            }
        } catch (\Throwable $e) {
            Log::error('TenantEvent 日志写入失败: ' . $e->getMessage());
        }
    }
}
