<?php

namespace app\validate\ration;

use think\Validate;

class HouseholdMemberValidate extends Validate
{
    protected $failException = true;

    protected $rule = [
        'id|ID' => 'require|integer',
        'apply_id|申报ID' => 'require|integer',
        'archive_id|档案ID' => 'integer',
        'czrzjhm|主承租人证件号码' => 'require|max:32',
        'name|姓名' => 'require|max:50',
        'zjlb|证件类别' => 'integer',
        'zjhm|证件号码' => 'require|max:32',
        'birth|出生日期' => 'date',
        'sex|性别' => 'require|in:0,1,2',
        'mz|民族' => 'integer',
        'marriage|婚姻状况' => 'require|integer',
        'lxdh|联系电话' => 'max:16',
        'jjlxrdh|紧急联系人电话' => 'max:32',
        'sfzj_file|证件影像文件路径' => 'max:512',
        'yzsqrgx|与主申请人关系' => 'require|integer',
        'is_gtsqr|是否共同申请人' => 'require|in:0,1',
        'hjszd|户籍所在地' => 'max:255',
        'hjszdxq|户籍所在地详情' => 'max:255',
        'is_bdhj|是否本地户籍' => 'in:0,1',
        'hjqr_date|户籍迁入日期' => 'date',
        'xjzszd|现居住所在地' => 'max:255',
        'jznx|居住年限' => 'integer',
        'jzzhm|居住证号码' => 'max:255',
        'jzzfzjg|居住证发证机关' => 'max:255',
        'jyqk|就业情况' => 'integer',
        'gzdw|工作单位' => 'max:128',
        'dwdz|单位地址' => 'max:255',
        'dwdh|单位电话' => 'max:32',
        'zylx|职业类型' => 'integer',
        'gzxz|工作性质' => 'integer',
        'bzqd_date|编制取得日期' => 'date',
        'jynx|就业年限' => 'integer',
        'ygxz|用工性质' => 'integer',
        'zcmc|职称名称' => 'max:255',
        'ht_start_date|劳动合同开始日期' => 'date',
        'ht_end_date|劳动合同结束日期' => 'date',
        'ysr|月收入' => 'float',
        'nsr|年收入' => 'float',
        'qt_nsr|其他年收入' => 'float',
        'jtnsr|家庭年收入' => 'float',
        'rjnsr|人均年收入' => 'float',
        'yhkh|银行卡号' => 'max:64',
        'txj|退休金养老金' => 'float',
        'gjj|公积金' => 'float',
        'ck|存款' => 'float',
        'qcgj|汽车估价' => 'float',
        'sybx|商业保险' => 'float',
        'qtcc|其他财产' => 'float',
        'fcjz|房产价值' => 'float',
        'jysr|经营净收入' => 'float',
        'syf|赡养费' => 'float',
        'fyf|抚养费' => 'float',
        'fyf_1|扶养费' => 'float',
        'whcd|文化程度' => 'integer',
        'by_date|毕业日期' => 'date',
        'sb_start_date|社保缴纳开始日期' => 'date',
        'sb_end_date|社保缴纳结束日期' => 'date',
        'is_dbry|是否低保人员' => 'max:255',
        'dbzhm|低保证号码' => 'max:255',
        'ssqt|所属特殊群体' => 'max:255',
        'is_poor_support|是否特困供养' => 'in:0,1',
        'is_veteran|是否退役军人' => 'in:0,1',
        'is_fire_rescue|是否消防救援人员' => 'in:0,1',
        'is_heroic|是否见义勇为' => 'in:0,1',
        'is_sanitation_worker|是否环卫职工' => 'in:0,1',
        'is_bus_worker|是否公交职工' => 'in:0,1',
        'is_teacher|是否教育工作者' => 'in:0,1',
        'is_medical_worker|是否医护人员' => 'in:0,1',
        'is_elderly|是否老年人' => 'in:0,1',
        'is_disabled_person|是否残疾人' => 'in:0,1',
        'is_special_family|是否计生特殊家庭' => 'in:0,1',
        'is_orphan|是否成年孤儿' => 'in:0,1',
        'is_rural_migrant|是否农业转移人口' => 'in:0,1',
        'child_count|子女数量' => 'integer',
        'is_serious_illness|是否患大病' => 'in:0,1',
        'is_disaster_victim|是否受灾家庭' => 'in:0,1',
        'is_yf|是否有房' => 'in:0,1',
        'yfxzmj|有房现住面积' => 'float',
        'yfrjmj|有房人均面积' => 'float',
        'cq_state|产权状态' => 'in:0,1',
        'cq_syr|登记的权利人' => 'max:255',
        'cq_htbh|登记编号或合同备案号' => 'max:64',
        'cq_fwzl|房屋坐落栋单元房号' => 'max:255',
        'cq_yt|用途' => 'max:255',
        'cqcx_time|产权查询时间' => 'date',
        'admin_id|操作管理员ID' => 'integer',
        'area_code|行政区划编码' => 'max:16',
        'bz|备注' => 'max:255',
    ];

    protected $message = [
        // 可自定义字段提示信息
    ];

    protected $scene = [
        'PreCheck' => [
            'name',
            'zjlb',
            'zjhm',
            'birth',
            'sex',
            'mz',
            'marriage',
            'lxdh',
            'jjlxrdh',
            'sfzj_file',
            'yzsqrgx',
            'is_gtsqr',
            'hjszd',
            'hjszdxq',
            'is_bdhj',
            'hjqr_date',
            'xjzszd',
            'jznx',
            'jzzhm',
            'jzzfzjg',
            'jyqk',
            'gzdw',
            'dwdz',
            'dwdh',
            'zylx',
            'gzxz',
            'bzqd_date',
            'jynx',
            'ygxz',
            'zcmc',
            'ht_start_date',
            'ht_end_date',
            'ysr',
            'nsr',
            'qt_nsr',
            'jtnsr',
            'rjnsr',
            'yhkh',
            'txj',
            'gjj',
            'ck',
            'qcgj',
            'sybx',
            'qtcc',
            'fcjz',
            'jysr',
            'syf',
            'fyf',
            'fyf_1',
            'whcd',
            'by_date',
            'sb_start_date',
            'sb_end_date',
            'is_dbry',
            'dbzhm',
            'ssqt',
            'is_poor_support',
            'is_veteran',
            'is_fire_rescue',
            'is_heroic',
            'is_sanitation_worker',
            'is_bus_worker',
            'is_teacher',
            'is_medical_worker',
            'is_elderly',
            'is_disabled_person',
            'is_special_family',
            'is_orphan',
            'is_rural_migrant',
            'child_count',
            'is_serious_illness',
            'is_disaster_victim',
            'is_yf',
            'yfxzmj',
            'yfrjmj',
            'cq_state',
            'cq_syr',
            'cq_htbh',
            'cq_fwzl',
            'cq_yt',
            'cqcx_time',
            'admin_id',
            'area_code',
            'bz',
        ],
    ];
}
