<?php

namespace app\validate;

use think\Validate;
use app\model\House;

class HouseValidate extends Validate
{
    protected $failException = true;

    protected $rule = [
        'id|ID' => 'require|integer',
        'xq_id|小区ID' => 'require|integer',
        'ld_id|楼栋ID' => 'require|integer',
        'xqmc|小区名称' => 'require|max:100',
        'ldmc|楼栋名称' => 'require|max:64',
        'dy|单元名称' => 'require|max:32',
        'lc|楼层' => 'require|integer',
        'myc|名义层' => 'max:64',
        'xh|序号' => 'require|number',
        'sh|室号' => 'require|max:32|isExist',
        'hx|户型' => 'require|integer|egt:1',
        'jzmj|建筑面积' => 'require|egt:0',
        'fwxz|房屋性质' => 'require|integer',
        'fwzt|房屋状态' => 'require|integer',
        'bz|备注' => 'max:255',

        'ids' => 'require',
    ];

    /**
     * 验证提示信息
     * @var array
     */
    protected $message = [];

    /**
     * 字段描述
     */
    protected $field = [];

    /**
     * 验证场景
     */
    protected $scene = [
        'add' => ['xq_id', 'ld_id', 'xqmc', 'ldmc', 'dy', 'lc', 'myc', 'xh', 'sh', 'hx', 'jzmj', 'fwxz', 'fwzt', 'bz'],
        'edit' => ['id', 'xq_id', 'ld_id', 'xqmc', 'ldmc', 'dy', 'lc', 'myc', 'xh', 'sh', 'hx', 'jzmj', 'fwxz', 'fwzt', 'bz'],
        'del' => ['id'],
        'batchDel' => ['ids'],
    ];

    /**
     * 是否已存在
     */
    protected function isExist($value, $rule, $data = [])
    {
        $mainModel = House::field('id');
        if (isset($data['id'])) {
            $mainModel->where('id', '<>', $data['id']);
        }
        $mainModel->where('xq_id', $data['xq_id'])
            ->where('ld_id', $data['ld_id'])
            ->where('dy', $data['dy'])
            ->where('lc', $data['lc'])
            ->where('xh', $data['xh']);

        $main = $mainModel->find();
        if ($main) {
            return "{$data['xqmc']}#{$data['ldmc']}:{$data['dy']}-{$data['lc']}-{$data['xh']}房屋已存在";
        }

        // TODO 其它验证 是否出租状态

        return true;
    }
}
