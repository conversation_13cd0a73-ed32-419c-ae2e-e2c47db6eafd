<?php

declare(strict_types=1);

namespace app\controller\front\http\wx;

use think\App;
use think\Request;
use think\response\Json;
use think\facade\Db;
use app\common\controller\FrontWx;
use app\controller\front\model\wx\WxUser;
use tools\Jwt;

class Login extends FrontWx
{
    protected $mini_app;
    private $config = [];

    public function __construct(App $app)
    {
        parent::__construct($app);

        $this->config = [
            'app_id' => env('WX.APP_ID'),
            'secret' => env('WX.APP_SECRET'),
            'token' => 'GzfWXToken',
            'aes_key' => '', // 明文模式请勿填写 EncodingAESKey

            'http' => [
                'throw' => true, // 状态码非 200、300 时是否抛出异常，默认为开启
                'timeout' => 5.0,
                'retry' => true, // 使用默认重试配置
            ],
        ];
        $this->mini_app = new \EasyWeChat\MiniApp\Application($this->config);
    }

    public function login(): Json
    {
        // 检查登录态
        if ($this->auth->isLogin()) {
            return $this->success('您已经登录过了，无需重复登录~', [
                'type' => $this->auth::LOGGED_IN
            ], $this->auth::LOGIN_RESPONSE_CODE);
        }

        $code = $this->request->post('code');
        if (!$code) {
            return $this->error('code不能为空');
        }
        try {
            $utils = $this->mini_app->getUtils();
            $wx_response = $utils->codeToSession($code);
        } catch (\Throwable $th) {
            return $this->error($th->getMessage());
        }

        $res = $this->auth->wxMiniProgramLogin($wx_response);
        if ($res === true) {
            return $this->success('登录成功', $this->auth->getInfo());
        } else {
            $msg = $this->auth->getError();
            $msg = $msg ?: '用户名或密码不正确';
            return $this->error($msg);
        }
    }

    public function userInfo(Request $request): Json
    {
        $wx_user = WxUser::where('id', $this->auth->id)->find();
        if ($wx_user) {
            // 隐藏 手机号 身份证 中间部分
            !empty($wx_user['phone']) && $wx_user['phone'] = substr_replace($wx_user['phone'], '****', 3, 4);
            !empty($wx_user['id_card']) && $wx_user['id_card'] = substr_replace($wx_user['id_card'], '********', 6, 8);
            unset($wx_user['openid']);

            return $this->success('获取成功', [
                'info' => $wx_user,
                'avatar' => ''
            ]);
        } else {
            return $this->error('用户不存在');
        }
    }

    public function updateUserInfo(Request $request): Json
    {
        $data = $this->request->only([
            // "nickname",
            'ext_info',
        ]);

        $wx_user = WxUser::where('id', $this->auth->id)->find();
        if ($wx_user) {
            $wx_user->save([
                'realname' => $data['ext_info']['name'] ?? '',
                'id_card' => $data['ext_info']['zjhm'] ?? '',
                'ext_info' => $data['ext_info'],
            ]);

            return $this->success('更新成功');
        } else {
            return $this->error('用户不存在');
        }
    }

    public function getUserPhoneNumber(): Json
    {
        $code = $this->request->post('code');
        if (empty($code)) {
            return $this->error('code不能为空');
        }

        try {
            $data = ['code' => (string)$code];

            $response = $this->mini_app->getClient()->postJson('wxa/business/getuserphonenumber', $data);

            // 检查响应是否成功
            if (isset($response['errcode']) && $response['errcode'] != 0) {
                return $this->error($response['errmsg'] ?? '获取手机号失败');
            }

            // 获取手机号信息
            $phoneInfo = $response['phone_info'] ?? null;
            if (!$phoneInfo) {
                return $this->error('未获取到手机号信息');
            }

            $phoneNumber = $phoneInfo['phoneNumber'] ?? '';
            $purePhoneNumber = $phoneInfo['purePhoneNumber'] ?? '';
            $countryCode = $phoneInfo['countryCode'] ?? '';

            // 如果用户已登录，则更新用户手机号
            if ($this->auth->isLogin()) {
                $wx_user = WxUser::where('id', $this->auth->id)->find();
                if (!$wx_user) {
                    return $this->error('用户不存在');
                }

                if ($wx_user->phone) {
                    return $this->success('success', $this->auth->getInfo());
                }

                $wx_user->save(['phone' => $purePhoneNumber,]);

                $payload = [
                    'user_id' => $this->auth->id,
                    'openid' => $this->auth->openid ?? '',
                    'phone' => $this->auth->phone ?? $purePhoneNumber,
                    'nickname' => $this->auth->nickname ?? '',
                ];
                $jwtTool = new Jwt();
                $newToken = $jwtTool->getToken($payload);
                $tokenData = $jwtTool->getTokenContent($newToken);

                return $this->success('success', array_merge($this->auth->getInfo(), [
                    'accessToken' => $newToken,
                    'expires' => date('Y/m/d H:i:s', (int)$tokenData['exp'])
                ]));
            } else {
                return $this->success('获取成功', [
                    'phone' => substr_replace($purePhoneNumber, '****', 3, 4)
                ]);
            }
        } catch (\Throwable $th) {
            return $this->error('获取手机号失败: ' . $th->getMessage());
        }
    }
}
