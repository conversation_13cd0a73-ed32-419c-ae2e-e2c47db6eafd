<?php

namespace app\controller\front\http\ration;

use app\common\controller\FrontWx as Frontend;
use app\controller\front\logic\ration\TenantReviewLogic;
use think\App;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\response\Json;

class TenantReview extends Frontend
{
    protected TenantReviewLogic $logic;

    public function __construct(APP $app, TenantReviewLogic $logic)
    {
        parent::__construct($app);

        $this->logic = $logic;
    }


    public function create(): Json
    {
        $params = $this->request->param();

        $data = $this->logic->create($params);
        return $this->success('success', $data);
    }

    /**
     * 获取合同详情
     * @throws ModelNotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     */
    public function detail(): Json
    {
        $id = $this->request->param('id');

        $data = $this->logic->getDetail($id);

        if (empty($data)) {
            return $this->error('数据不存在');
        }

        return $this->success('success', $data);
    }

}
