<?php

namespace app\controller\front\http\ration;

use app\common\controller\FrontWx as Frontend;
use app\controller\front\logic\ration\TenantLeaseLogic;
use think\App;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\response\Json;

class TenantLease extends Frontend
{
    protected TenantLeaseLogic $logic;

    public function __construct(APP $app, TenantLeaseLogic $logic)
    {
        parent::__construct($app);

        $this->logic = $logic;
    }

    /**
     * @throws DbException
     */
    public function index(): Json
    {
        $where = [];
        $limit = $this->request->param('limit/d', 15);
        $params = $this->request->param();

        isset($params['type']) && $params['type'] != '' && $where[] = ['type', '=', $params['type']];
        isset($params['status']) && $params['status'] != '' && $where[] = ['status', '=', $params['status']];

        $zjhm = $this->auth->id_card ?? '';

        $fields = "Lease.*";

        $data = $this->logic->setBeforeGetList(function ($query) use ($zjhm) {
            return $query->hasWhere('archive', ['czrzjhm' => $zjhm]);
        })->getList($where, $fields, 'id desc', $limit);

        return $this->success('success', $data);
    }

    /**
     * 获取租赁详情
     * @throws ModelNotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     */
    public function detail(): Json
    {
        $id = $this->request->param('id');

        $data = $this->logic->getDetail($id);

        return $this->success('success', $data);
    }

}
