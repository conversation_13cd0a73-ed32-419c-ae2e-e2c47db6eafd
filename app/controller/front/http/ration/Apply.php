<?php

namespace app\controller\front\http\ration;

use app\enums\ration\ApplyStatusEnum;
use think\App;
use think\exception\ValidateException;
use think\response\Json;
use app\common\controller\FrontWx as Frontend;

use app\controller\backend\validate\ration\ApplyValidate;
use app\controller\backend\validate\ration\FamilyMemberValidate;
use app\controller\front\logic\ration\ApplyLogic;
use app\controller\backend\model\ration\Apply as ApplyModel;

class Apply extends Frontend
{
    protected ApplyLogic $logic;

    public function __construct(APP $app, ApplyLogic $logic)
    {
        parent::__construct($app);

        $this->logic = $logic;
        $this->logic->setUid($this->auth->id);
    }

    public function index(): Json
    {
        $where = [];
        $limit = $this->request->param('limit/d', 15);
        $params = $this->request->param();

        // 只能看自己的
        $where[] = ['wx_uid', '=', $this->auth->id];

        isset($params['qc_id']) && $params['qc_id'] != '' && $where[] = ['qc_id', '=', $params['qc_id']];
        isset($params['nian']) && $params['nian'] != '' && $where[] = ['nian', '=', $params['nian']];
        isset($params['qi']) && $params['qi'] != '' && $where[] = ['qi', '=', $params['qi']];
        isset($params['sblx']) && $params['sblx'] != '' && $where[] = ['sblx', '=', $params['sblx']];
        isset($params['fylx']) && $params['fylx'] != '' && $where[] = ['fylx', '=', $params['fylx']];
        isset($params['sbtj']) && $params['sbtj'] != '' && $where[] = ['sbtj', '=', $params['sbtj']];
        isset($params['xq_id']) && $params['xq_id'] != '' && $where[] = ['xq_id', '=', $params['xq_id']];
        isset($params['xqmc']) && $params['xqmc'] != '' && $where[] = ['xqmc', 'like', "%{$params['xqmc']}%"];
        isset($params['czr']) && $params['czr'] != '' && $where[] = ['czr', 'like', "%{$params['czr']}%"];
        isset($params['czrzjhm']) && $params['czrzjhm'] != '' && $where[] = ['czrzjhm', 'like', "%{$params['czrzjhm']}%"];


        $data = $this->logic->getList($where, '*', 'id desc', $limit);

        return $this->success('success', $data);
    }

    public function add(): Json
    {
        try {
            $check_apply = $this->checkApply()->getData();
            if ($check_apply['code'] != 200) {
                return $this->error($check_apply['msg']);
            }
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }

        $data = $this->request->only([
            'qc_id',
            'nian',
            'qi',
            'sblx',
            'sbqy',
            'fylx',
            'czr',
            'sex',
            'czrzjhm',
            'czrlxdh',
            'czrgzdw',
            'sqr_info',
            'family_info',
            'other_info',
            'sb_date',
            'xq_id',
            'sbxq',
            'sbhx',
            'bzfs',
            'bz',
            'zm_files',
            // 'sl_date',
            // 'hzbh'
        ]);
        $data['sbtj'] = ApplyModel::SBTJ['ONLINE'][0];
        $data['status'] = ApplyStatusEnum::DRAFT->value;
        $data['add_user'] = '';
        $data['wx_uid'] = $this->auth->id;

        try {
            validate(FamilyMemberValidate::class)->scene('PreCheck')->check($data['sqr_info']);
        } catch (ValidateException $e) {
            return $this->error('申请人信息：' . $e->getMessage());
        }

        if (!is_array($data['family_info'])) {
            return $this->error('家庭成员信息：格式错误,应为数组格式');
        }
        if (!empty($data['family_info'])) {
            foreach ($data['family_info'] as $item) {
                try {
                    validate(FamilyMemberValidate::class)->scene('PreCheck')->check($item);
                } catch (ValidateException $e) {
                    return $this->error('家庭成员信息：' . ($item['name'] ?? '') . ' ' . $e->getMessage());
                }
            }
        }

        try {
            validate(ApplyValidate::class)->scene('add')->check($data);
            $result = $this->logic->add($data);
            return $this->success('添加成功', $result);
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }

    public function edit(): Json
    {
        $data = $this->request->only([
            'id',
            'qc_id',
            'nian',
            'qi',
            'sblx',
            'sbqy',
            'fylx',
            'sbtj',
            'czr',
            'sex',
            'czrzjhm',
            'czrlxdh',
            'czrgzdw',
            'sqr_info',
            'family_info',
            'other_info',
            'sb_date',
            'xq_id',
            'sbxq',
            'sbhx',
            'bzfs',
            'status',
            // 'add_user',
            'sl_date',
            // 'hzbh',
            'bz',
            'zm_files'
        ]);
        if (!$data) {
            return $this->error('参数不能为空');
        }

        try {
            validate(ApplyValidate::class)->scene('edit')->check($data);
            $this->logic->edit($data);
            return $this->success('更新成功');
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }

    public function detail(): Json
    {
        $data = $this->request->only([
            'id'
        ]);
        $data = $this->logic->detailOverwrite($data['id'], $this->auth->id);

        return $this->success('success', $data);
    }

    public function del(): Json
    {
        $data = $this->request->only([
            'id'
        ]);

        try {
            validate(ApplyValidate::class)->scene('del')->check($data);
            $this->logic->delOverwrite($data, $this->auth->id);
            return $this->success('删除成功');
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }

    public function checkApply(): Json
    {
        $data = $this->request->only([
            'id' => '',
            'qc_id',
            'czrzjhm',
            'xq_id',
            'sbhx',
        ]);

        try {
            validate(ApplyValidate::class)->scene('checkApply')->check($data);
            $re_data = $this->logic->checkApply($data);

            if ($re_data !== true) {
                return $this->error($re_data);
            } else {
                return $this->success('通过检查');
            }
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }

    public function stage(): Json
    {
        try {
            $check_apply = $this->checkApply()->getData();
            if ($check_apply['code'] != 200) {
                return $this->error($check_apply['msg']);
            }
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }

        $data = $this->request->only([
            'id',
            'qc_id',
            'nian',
            'qi',
            'sblx',
            'sbqy',
            'fylx',
            'sbtj',
            'czr',
            'sex',
            'czrzjhm',
            'czrlxdh',
            'czrgzdw',
            'sqr_info',
            'family_info',
            'other_info',
            'sb_date',
            'xq_id',
            'sbxq',
            'sbhx',
            'bzfs',
            // 'status',
            // 'add_user',
            'sl_date',
            // 'hzbh',
            'bz',
            'zm_files'
        ]);
        // $data['add_user'] = $this->auth->username;
        $data['wx_uid'] = $this->auth->id;


        try {
            validate(FamilyMemberValidate::class)->scene('PreCheck')->check($data['sqr_info']);
        } catch (ValidateException $e) {
            return $this->error('申请人信息：' . $e->getMessage());
        }

        foreach ($data['family_info'] as $item) {
            try {
                validate(FamilyMemberValidate::class)->scene('PreCheck')->check($item);
            } catch (ValidateException $e) {
                return $this->error('家庭成员信息：' . ($item['name'] ?? '') . ' ' . $e->getMessage());
            }
        }

        try {
            validate(ApplyValidate::class)->scene('stage')->check($data);
            $this->logic->stage($data);
            return $this->success('暂存成功');
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }

    public function tjsp(): Json
    {
        $data = $this->request->only([
            'id'
        ]);
        $data['wx_uid'] = $this->auth->id;

        try {
            validate(ApplyValidate::class)->scene('tjsp')->check($data);
            $this->logic->tjsp($data);
            return $this->success('提交审批成功');
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }
}
