<?php

namespace app\controller\front\http;

use app\common\controller\FrontWx as Frontend;
use app\controller\backend\model\StreetCode;
use think\response\Json;

class Index extends Frontend
{

    /**
     * 系统配置
     * @return Json
     */
    public function config(): <PERSON>son
    {
        $data = [
            'resource_domain' => env('MINIO.CDN_URL', 'https://wdy.minio.allesm.cn'),
        ];

        return $this->success('success', $data);
    }

    /**
     * 获取管理区域代码
     * @return Json
     */
    public function getAreasCode(): Json
    {
        $data = [
            ['code' => '4203', 'name' => '市本级', 'lng' => '110.789787', 'lat' => '32.645358'],
            ['code' => '420304', 'name' => '郧阳区', 'lng' => '110.813341', 'lat' => '32.835507'],
            ['code' => '420322', 'name' => '郧西县', 'lng' => '110.426060', 'lat' => '32.993090'],
            ['code' => '420323', 'name' => '竹山县', 'lng' => '110.228923', 'lat' => '32.224817'],
            ['code' => '420324', 'name' => '竹溪县', 'lng' => '109.715330', 'lat' => '32.318207'],
            ['code' => '420325', 'name' => '房县', 'lng' => '110.726678', 'lat' => '32.040147'],
            ['code' => '420381', 'name' => '丹江口市', 'lng' => '111.513271', 'lat' => '32.540191'],
        ];

        return $this->success('success', $data);
    }

    /**
     * 获取行政区域代码
     * @return Json
     */
    public function getRegionCode(): Json
    {
        $pid = $this->request->param('pid');

        $where[] = ['status', '=', 1];

        isset($pid) && is_numeric($pid) && $where[] = ['pid', '=', $pid];

        $list = StreetCode::where($where)
            ->order('sort asc, id asc')
            ->field('id,code,name,pid,level')
            ->cache('region_code_' . $pid, 86400 * 7)
            ->select()
            ->toArray();

        return $this->success('success', $list);
    }
}
