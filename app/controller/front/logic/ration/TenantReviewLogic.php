<?php

namespace app\controller\front\logic\ration;

use app\ApiException;
use app\controller\backend\model\ration\ContractRenewApply;
use app\controller\front\traits\BaseOperateTrait;
use app\service\TenantReviewService;
use think\Model;

class TenantReviewLogic
{
    use BaseOperateTrait;

    protected ContractRenewApply $model;

    public function __construct()
    {
        $this->model = new ContractRenewApply();
    }

    public function create($params): Model|ContractRenewApply
    {
        try {
            return (new TenantReviewService())->handleTenantReview($params);
        } catch (\Exception $e) {
            throw new ApiException($e->getMessage());
        }
    }
}
