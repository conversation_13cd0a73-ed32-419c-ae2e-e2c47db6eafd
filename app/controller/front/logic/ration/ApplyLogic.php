<?php

namespace app\controller\front\logic\ration;

use app\enums\ration\ApplyStatusEnum;
use app\service\ApplyService;
use Exception;
use think\facade\Db;
use think\Model;
use app\ApiException;
use app\controller\backend\logic\AdminLogicStrategy;
use app\controller\backend\model\ration\Apply;
use app\controller\backend\logic\ration\PeriodLogic;

use app\controller\backend\model\ration\Archive;
use app\controller\backend\model\ration\FamilyMember;
use app\controller\backend\logic\ration\LeaseLogLogic;
use app\controller\backend\model\ration\LeaseLog;
use app\controller\backend\model\ration\Period;


class ApplyLogic extends AdminLogicStrategy
{

    public function __construct()
    {
        $this->model = new Apply();

        parent::__construct($this->model);
    }

    public function add(array $data): Model
    {
        Db::startTrans();
        try {
            // 查询期次
            $qici = Period::where('id', $data['qc_id'])->find();
            if (!$qici) {
                throw new ApiException('期次不存在');
            }
            $data['qy_id'] = $qici['qy_id'];
            $data['area_code'] = $qici['area_code'];

            if (empty($data['zm_files'])) {
                unset($data['zm_files']);
            }

            $applyResult = $this->model->create($data);

            if ($applyResult->id) {
                (new LeaseLogLogic)->record(LeaseLog::TYPE['XSSQ'], ['apply_id' => $applyResult->id]);
            }

            Db::commit();
            return $applyResult;
        } catch (Exception $e) {
            Db::rollback();
            throw new ApiException($e->getMessage());
        }
    }

    public function detailOverwrite(int $id, int $wx_uid)
    {
        $where = [];
        $where[] = ['wx_uid', '=', $wx_uid];

        return $this->model->where($where)->find($id);
    }

    public function delOverwrite(int|string|array $id, int $wx_uid): bool
    {
        $where = [];

        $where[] = ['id', 'in', $id];
        $where[] = ['wx_uid', '=', $wx_uid];

        $result = $this->model->where($where)->field('id')->select();
        if (!$result->isEmpty()) {
            foreach ($result as $item) {
                $item->delete();
            }
        }

        return true;
    }


    public function checkApply($data)
    {
        // $data里参数require在控制器的验证器已验证

        // 申请的期次是否存在 + 本次期次是否已申请 +本期次小区+户型 申报数量是否已满
        $period = (new PeriodLogic)->setUid($this->uid)->getOne([
            'id' => $data['qc_id'],
            'status' => '1'
        ]);

        if (!$period) {
            return '期次查询失败';
        } else {
            // 判断期次是否还在可申报时间内
            $start = strtotime($period['start_time']);
            $end = strtotime($period['end_time']);
            $now = time();
            if ($now < $start) {
                return '申报期次未开始';
            }
            if ($now > $end) {
                return '申报期次已结束';
            }

            $period_config = $period['config'];
            $is_xq_hx_exist = false; // 是否存在小区+户型
            foreach ($period_config as $cfg) {
                if ($cfg['xq_id'] == $data['xq_id'] && $cfg['hx'] == $data['sbhx']) {
                    $is_xq_hx_exist = true;
                    if ($cfg['max_num'] != 0 && $cfg['ysq_num'] >= $cfg['max_num']) {
                        return "{$cfg['xq_mc']}[{$cfg['hx_mc']}] {$period['nian']}年{$period['qi']}期 只允许 {$cfg['max_num']} 个申报";
                    }
                }
            }
            if (!$is_xq_hx_exist) {
                return '期次未配置小区+户型';
            }
        }

        // 申报人或者申报人家庭成员已经存在申请
        $applyModel = $this->model->where(function ($query) use ($data) {
            $query->whereOr([
                ['czrzjhm', 'like', "%{$data['czrzjhm']}%"],
                ['family_info', 'like', "%{$data['czrzjhm']}%"]
            ]);
        });

        if (isset($data['id']) && $data['id'] != '') {
            $applyModel->where(function ($query) use ($data) {
                $query->where('id', '<>', $data['id']);
            });
        }

        $apply = $applyModel->find();
        if ($apply) {
            return '申报人或者申报人家庭成员已经存在';
        }


        // TODO: 公租房档案检查

        // TODO: 廉租房档案检查

        // TODO: 申报人在'.date("Y年m月d日",$out).'前内禁止新申报' 退房等情况

        // TODO: 信息封存?

        // TODO: 黑名单检查

        return true;
    }

    public function stage($data)
    {
        return $this->edit($data);
    }

    public function tjsp($data): bool
    {
        Db::startTrans();
        try {
            $apply = Apply::where('id', $data['id'])
                ->where('wx_uid', $data['wx_uid'])
                ->find();
            if (!$apply) {
                throw new ApiException('申报记录不存在');
            }

            if (!in_array($apply['status'], [ApplyStatusEnum::DRAFT->value, ApplyStatusEnum::RETURNED->value])) {
                throw new ApiException('业务审核中');
            }

            $apply->status = ApplyStatusEnum::SUBMITTED->value;

            $apply->save();

            (new ApplyService())->approve($data['id']);

            (new LeaseLogLogic)->record(LeaseLog::TYPE['SQTJSP'], ['apply_id' => $apply->id]);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }

        return true;
    }

}
