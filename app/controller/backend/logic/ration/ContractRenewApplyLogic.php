<?php

namespace app\controller\backend\logic\ration;

use app\enums\ration\ContractStatusEnum;
use app\enums\ration\LeaseStatusEnum;
use app\enums\ration\LeaseTypeEnum;
use Carbon\Carbon;
use think\facade\Db;
use app\ApiException;
use app\controller\backend\model\ration\Lease;
use app\controller\backend\model\ration\Contract;
use app\controller\backend\logic\AdminLogicStrategy;
use app\controller\backend\model\ration\ContractRenewApply;

class ContractRenewApplyLogic extends AdminLogicStrategy
{

    public function __construct()
    {
        $this->model = new ContractRenewApply();

        parent::__construct($this->model);
    }

    public function finished($params): void
    {
        // todo 查询要完成的年审记录
        $row = $this->model->findOrEmpty($params['id']);
        if ($row->isEmpty()) {
            throw new ApiException('年审记录不存在');
        }

        if ($row->status != ContractRenewApply::STATUS['DSP'][0]) {
            throw new ApiException('年审记录状态不正确，无法完成');
        }

        // 当前租赁记录
        $currentLease = Lease::findOrEmpty($row->lease_id);
        if ($currentLease->isEmpty()) {
            throw new ApiException('承租记录不存在');
        }

        Db::startTrans();
        try {
            // 当前合同
            $currentContract = Contract::findOrEmpty($row->contract_id);
            if ($currentContract->isEmpty()) {
                throw new ApiException('合同记录不存在');
            }

            // 计算新的合同开始和结束日期（开始和结束当天都生效）
            $new_ht_ksrq = Carbon::parse($currentContract->ht_jsrq)->addDay()->format('Y-m-d');
            $new_ht_jsrq = Carbon::parse($new_ht_ksrq)->addYear()->subDay()->format('Y-m-d');

            $lease_temp = [
                'status' => LeaseStatusEnum::PENDING->value,// 待签约
                'type' => LeaseTypeEnum::NS->value,
                'xq_id' => $currentLease->xq_id ?: $currentContract->xq_id,
                'add_user' => '',
                'pz_user' => '',
                'pz_time' => null,
                'zj_etime' => null,
                'create_time' => time(),
                'update_time' => time(),
            ];
            $lease_data = array_merge($currentLease->toArray(), $lease_temp);
            unset($lease_data['id']); // 删除ID，避免更新原记录
            // 创建配租记录
            $newLease = Lease::createLeaseRecord($lease_data);

            $contract_temp = [
                'lease_id' => $newLease->id,
                'status' => ContractStatusEnum::PENDING->value,// 待签约
                'ht_lx' => Contract::HTLX['XU'][0],
                'ht_qdsj' => null,
                'ht_ksrq' => $new_ht_ksrq,
                'ht_jsrq' => $new_ht_jsrq,
                'price' => '0.00',
                'rent' => '0.00',
                'zj' => 0,
                'add_user' => '',
                'admin_id' => $this->uid ?? 0,
                'create_time' => time(),
                'update_time' => time(),
            ];

            $contract_data = array_merge($currentContract->toArray(), $contract_temp);
            unset($contract_data['id']);
            // 创建合同
            $newContract = Contract::create($contract_data);

            // 更新年审记录状态
            $row->new_lease_id = $newLease->id;
            $row->new_contract_id = $newContract->id;
            $row->ht_ksrq = $new_ht_ksrq;
            $row->ht_jsrq = $new_ht_jsrq;
            $row->status = ContractRenewApply::STATUS['TG'][0];
            $row->sp_user = '';
            $row->sp_time = date('Y-m-d H:i:s');
            $row->sp_yj = '';
            $row->save();

            // 更新当前配租的状态
            $currentLease->status = LeaseStatusEnum::RENEWED->value;
            $currentLease->save();

            Db::commit();

        } catch (\Throwable $e) {
            Db::rollback();
            throw new ApiException('操作失败: ' . $e->getMessage());
        }

    }
}
