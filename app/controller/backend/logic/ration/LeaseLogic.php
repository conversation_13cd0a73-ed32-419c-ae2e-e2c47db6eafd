<?php

namespace app\controller\backend\logic\ration;

use app\ApiException;
use app\controller\backend\logic\AdminLogicStrategy;
use app\controller\backend\model\ration\Archive;
use app\controller\backend\model\ration\Contract;
use app\controller\backend\model\ration\Lease;
use app\controller\backend\model\ration\LeaseLog;
use app\enums\ration\ArchiveStatusEnum;
use app\enums\ration\ContractStatusEnum;
use app\enums\ration\LeaseStatusEnum;
use app\enums\ration\LeaseTypeEnum;
use app\model\Estate;
use app\model\House;
use app\model\ration\TenantCheckoutRecord;
use app\service\FinanceService;
use Exception;
use think\facade\Db;


class LeaseLogic extends AdminLogicStrategy
{

    public function __construct()
    {
        $this->model = new Lease();

        parent::__construct($this->model);
    }

    public function ruzhu($data): bool
    {
        Db::startTrans(); // 启动事务
        try {
            // TODO: 是否可入住 状态 租金已交 限定各种状态 控制操作
            $leases = Lease::where('id', 'in', $data['ids'])->select();
            if ($leases->isEmpty()) {
                throw new ApiException('没有找到对应的配租');
            }

            foreach ($leases as $lease) {

                $fw = House::find($lease['fw_id']);
                if (!$fw) {
                    throw new ApiException('房屋不存在');
                }
                // 更新房屋状态
                $fw->save([
                    'fwzt' => House::FWZT['YRZ'][0],
                ]);

                $archive = Archive::find($lease['archive_id']);
                if (!$archive) {
                    throw new ApiException('申请档案不存在');
                }
                // 更新申请档案状态
                $archive->save([
                    'status' => Archive::STATUS['YRZ'][0],
                ]);

                // 更新配租状态
                $lease->save([
                    'bz_time' => date('Y-m-d H:i:s'),
                    'status' => Archive::STATUS['YRZ'][0],
                ]);

                // 更新合同状态
                $contract = Contract::find($lease['contract_id']);
                if (!$contract) {
                    throw new ApiException('合同不存在');
                }
                $contract->save([
                    'status' => Contract::STATUS['ZXZ'][0],
                ]);

                (new LeaseLogLogic)->record(LeaseLog::TYPE['QRRZ'], [
                    'add_user' => $data['op_user'],
                    'archive_id' => $lease->archive_id,
                    'lease_id' => $lease->id,
                    'fw_id' => $lease->fw_id,
                    'contract_id' => $lease->contract_id,
                ]);
            }


            Db::commit(); // 提交事务
        } catch (\Exception $e) {
            Db::rollback(); // 回滚事务
            throw $e;
        }

        return true;
    }

    /**
     * 导入摇号结果
     * @throws \Exception
     */
    public function importLotteryResults($data): void
    {
        $all_zgbh = array_column($data['yaohao_info'], 'zgbh');

        // 查询摇号结果对应的档案信息
        $archives = Archive::where('zgbh', 'in', $all_zgbh)->column('*', 'zgbh');

        $lottery_selected = [];
        $waiting_list = [];
        foreach ($data['yaohao_info'] as $item) {
            $fw_id = $item['fw_id'] ?? null;
            $zgbh = $item['zgbh'] ?? null;

            if (!is_numeric($fw_id) || !$zgbh) {
                throw new ApiException('摇号结果数据错误1');
            }

            $archive = $archives[$item['zgbh']] ?? null;
            if (!$archive) {
                throw new ApiException('摇号结果数据错误2');
            }

            $temp = [
                'zgbh' => $zgbh,
                'pz_user' => $data['pz_user'] ?? '',
                'area_code' => $data['area_code'] ?? '',
                'fw_id' => $fw_id,
            ];

            if ($fw_id > 0) {
                $htks_time = $item['htks_time'] ?? null;
                $htjs_time = $item['htjs_time'] ?? null;
                if (!$htks_time || !$htjs_time) {
                    throw new ApiException('摇号结果数据错误3');
                }

                $lottery_selected[] = array_merge($temp, [
                    'fw_id' => $fw_id,
                    'htks_time' => $item['htks_time'],
                    'htjs_time' => $item['htjs_time'],
                    'archive' => $archive,
                ]);
            } else if ($fw_id == 0) {
                $lhsx = $item['lhsx'] ?? null;
                $lhcs_time = $item['lhcs_time'] ?? null;
                if (!$lhsx || !$lhcs_time) {
                    throw new ApiException('摇号结果数据错误4');
                }

                $waiting_list[] = array_merge($temp, [
                    'lhsx' => $item['lhsx'],
                    'lhcs_time' => $item['lhcs_time'],
                    'archive' => $archive,
                ]);
            }
        }

        Db::startTrans();
        try {
            if (!empty($lottery_selected)) {
                $this->importLotterySelected($lottery_selected);
            }

            if (!empty($waiting_list)) {
                $this->importWaitingList($waiting_list);
            }

            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 导入摇中结果
     * @throws Exception
     */
    public function importLotterySelected($lottery_selected): void
    {
        $fw_ids = array_column($lottery_selected, 'fw_id');
        $houses = House::where('id', 'in', $fw_ids)->with('building')->column('*', 'id');

        foreach ($lottery_selected as $item) {
            $house = $houses[$item['fw_id']] ?? null;
            if (!$house) {
                throw new Exception('摇号结果数据错误5');
            }

            $lease_temp = [
                'archive_id' => $item['archive']['id'],
                'status' => LeaseStatusEnum::PENDING->value,// 待签约
                'type' => LeaseTypeEnum::XCYZ->value,
                'pz_user' => $item['pz_user'],
                'pz_time' => date('Y-m-d H:i:s'),
                'area_code' => $item['area_code'],
                'xq_id' => $house['xq_id'],
                'xq_mc' => $house['xqmc'],
                'mph' => $house['ldmc'] . '#' . $house['sh'],
                'fw_id' => $item['fw_id'],
            ];

            // 创建配租记录
            $lease = Lease::createLeaseRecord($lease_temp);

            $contract_temp = [
                'lease_id' => $lease->id,
                'archive_id' => $lease->archive_id,
                'status' => ContractStatusEnum::PENDING->value,// 待签约
                'czr' => $item['archive']['czr'],
                'xq_id' => $house['xq_id'],
                'ld_id' => $house['ld_id'],
                'fw_id' => $house['id'],
                'xqmc' => $house['xqmc'],
                'ldmc' => $house['ldmc'],
                'dy' => $house['dy'],
                'sh' => $house['sh'],
                'jzmj' => $house['jzmj'],
                'hx' => $house['hx'],
                'ht_lx' => Contract::HTLX['XQ'][0],
                'ht_ksrq' => $item['htks_time'],
                'ht_jsrq' => $item['htjs_time'],
                'price' => 0.00,
                'rent' => 0.00,
                'zj' => 0,
                'yj' => 0,
                'add_user' => $item['pz_user'],
                'area_code' => $item['area_code'],
                'admin_id' => $this->uid ?? 0
            ];

            // 创建合同
            Contract::create($contract_temp);

            // 更新房屋状态
            House::update(['fwzt' => House::FWZT['YPZ'][0]], ['id' => $lease->fw_id]);

            // 更新档案状态
            Archive::update(['status' => ArchiveStatusEnum::LOTTERY_SELECTED], ['id' => $lease->archive_id]);

            // todo 记录日志
        }
    }

    /**
     * 导入轮候结果
     * @throws Exception
     */
    public function importWaitingList($waiting_list): void
    {
        $xq_fydm = Estate::column('id,fydm', 'id');

        foreach ($waiting_list as $item) {
            $archive_id = $item['archive']['id'];
            $xq_id = $item['archive']['xq_id'];
            $fydm = $xq_fydm[$xq_id]['fydm'] ?? null;
            if (!$fydm) {
                throw new Exception('摇号结果数据错误');
            }

            $lhbh = date("Y") . $fydm . str_pad($item['lhsx'], 3, "0", STR_PAD_LEFT);

            // 更新档案状态
            Archive::update(
                ['status' => ArchiveStatusEnum::WAITING_LIST->value, 'lhbh' => $lhbh, 'lh_time' => $item['lhcs_time']],
                ['id' => $archive_id]
            );

            // todo 记录日志
        }
    }

    /**
     * 获取退租预计算数据
     * @throws Exception
     */
    public function beforeCheckout($data): array
    {
        try {
            if (empty($data['contract_id']) || empty($data['lease_id'])) {
                throw new \Exception('缺少必要参数');
            }

            return (new FinanceService())->preCalculateBeforeCheckout(
                $data['contract_id'],
                $data['checkout_date'] ?? null);
        } catch (\Exception $e) {
            throw new ApiException('获取退租预计算数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 退房操作
     * @param array $data
     * @return array
     * @throws ApiException
     */
    public function checkout($data): array
    {
        try {
            return (new FinanceService())->checkoutRefund($data);
        } catch (\Exception $e) {
            throw new ApiException('退房失败: ' . $e->getMessage());
        }
    }

    /**
     * 退房完成
     * @param array $data
     * @throws ApiException
     */
    public function checkoutCompleted($data): void
    {
        Db::startTrans();
        try {
            $checkout = TenantCheckoutRecord::findOrEmpty($data['id']);

            if ($checkout->isEmpty()) {
                throw new \Exception('退房记录不存在');
            }

            $checkout->status = 1;
            $checkout->save();

            // 更新合同状态
            $contract = Contract::findOrEmpty($checkout->contract_id);
            if ($contract->isEmpty()) {
                throw new \Exception('合同记录不存在');
            }
            $contract->status = ContractStatusEnum::TERMINATED->value;
            $contract->save();

            // 更新租户状态
            $lease = Lease::findOrEmpty($checkout->lease_id);
            if ($lease->isEmpty()) {
                throw new \Exception('配租记录不存在');
            }
            $lease->status = LeaseStatusEnum::CHECKED_OUT->value;
            $lease->save();

            // 更新档案状态
            $archive = Archive::findOrEmpty($lease->archive_id);
            if ($archive->isEmpty()) {
                throw new \Exception('档案记录不存在');
            }
            $archive->status = ArchiveStatusEnum::CHECKED_OUT->value;
            $archive->save();

            // 更新房屋状态
            $house = House::findOrEmpty($lease->fw_id);
            if ($house->isEmpty()) {
                throw new \Exception('房屋记录不存在');
            }
            $house->fwzt = House::FWZT['KZ'][0]; // 可租用状态
            $house->save();

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            throw new ApiException('退房操作失败: ' . $e->getMessage());
        }
    }
}
