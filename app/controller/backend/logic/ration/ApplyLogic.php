<?php

namespace app\controller\backend\logic\ration;

use app\ApiException;
use app\controller\backend\logic\AdminLogicStrategy;
use app\controller\backend\model\ration\Apply;
use app\controller\backend\model\ration\Apply as ApplyModel;
use app\controller\backend\model\ration\Archive;
use app\controller\backend\model\ration\FamilyMember;
use app\controller\backend\model\ration\LeaseLog;
use app\controller\backend\model\ration\Period;
use app\enums\ration\ApplyStatusEnum;
use app\enums\ration\ApprovalStatusEnum;
use app\enums\ration\ArchiveStatusEnum;
use app\model\AppMessage;
use app\service\ApplyService;
use Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\Model;

class ApplyLogic extends AdminLogicStrategy
{

    public function __construct()
    {
        $this->model = new Apply();

        parent::__construct($this->model);
    }


    public function checkApply($data)
    {
        // $data里参数require在控制器的验证器已验证

        // 申请的期次是否存在 + 本次期次是否已申请 +本期次小区+户型 申报数量是否已满
        $period = (new PeriodLogic)->setUid($this->uid)->getOne([
            'id' => $data['qc_id'],
            'status' => '1'
        ]);

        if (!$period) {
            return '期次查询失败';
        } else {
            $period_config = $period['config'];
            $is_xq_hx_exist = false; // 是否存在小区+户型
            foreach ($period_config as $cfg) {
                if ($cfg['xq_id'] == $data['xq_id'] && $cfg['hx'] == $data['sbhx']) {
                    $is_xq_hx_exist = true;
                    if ($cfg['max_num'] != 0 && $cfg['ysq_num'] >= $cfg['max_num']) {
                        return "{$cfg['xq_mc']}[{$cfg['hx_mc']}] {$period['nian']}年{$period['qi']}期 只允许 {$cfg['max_num']} 个申报";
                    }
                }
            }
            if (!$is_xq_hx_exist) {
                return '期次未配置小区+户型';
            }
        }

        // 申报人或者申报人家庭成员已经存在申请
        $applyModel = $this->model->where(function ($query) use ($data) {
            $query->whereOr([
                ['czrzjhm', 'like', "%{$data['czrzjhm']}%"],
                ['family_info', 'like', "%{$data['czrzjhm']}%"]
            ]);
        });

        if (isset($data['id']) && $data['id'] != '') {
            $applyModel->where(function ($query) use ($data) {
                $query->where('id', '<>', $data['id']);
            });
        }

        $apply = $applyModel->find();
        if ($apply) {
            return '申报人或者申报人家庭成员已经存在';
        }


        // TODO: 公租房档案检查

        // TODO: 廉租房档案检查

        // TODO: 申报人在'.date("Y年m月d日",$out).'前内禁止新申报' 退房等情况

        // TODO: 信息封存?

        // TODO: 黑名单检查

        return true;
    }

    public function stage($data): Model
    {
        if (isset($data['id']) && $data['id'] != '') {
            return $this->edit($data);
        } else {
            return $this->add($data);
        }
    }

    public function add($data): Model
    {
        Db::startTrans();
        try {
            // 查询期次模型
            $qici = Period::find($data['qc_id']);
            if (!$qici) {
                throw new Exception('期次不存在');
            }

            if (empty($data['zm_files'])) {
                unset($data['zm_files']);
            }

            $user = request()->x_token_user;

            if ($this->model->isEnableDataScope()) {
                $data['admin_id'] = $user->id ?? null;

                // 如果区域编码为空，则尝试自动设置
                $data['area_code'] = $data['area_code'] ?? $user->dept->area_code ?? null;
            }

            $applyResult = $this->model->create($data);

            if ($data['status'] == ApplyStatusEnum::SUBMITTED->value) {
                (new ApplyService())->approve($applyResult->id, $this->uid);
            }

            Db::commit();
            return $applyResult;
        } catch (Exception $e) {
            Db::rollback();
            throw new ApiException($e->getMessage());
        }
    }

    public function edit(array $data): Model
    {
        Db::startTrans();
        try {
            $row = $this->model->find($data['id']);
            if (!$row) {
                throw new ApiException('记录未找到');
            }

            $applyResult = $this->model->update($data);

            if ($data['status'] == ApplyStatusEnum::SUBMITTED->value) {
                (new ApplyService())->approve($applyResult->id, $this->uid);
            }

            Db::commit();
            return $applyResult;
        } catch (Exception $e) {
            Db::rollback();
            throw new ApiException($e->getMessage());
        }
    }

    /**
     * 任务退回挂起
     * @param $business_no
     * @param $task
     * @param $remark
     * @return void
     */
    public function pending($business_no, $task, $remark = ''): void
    {
        $this->model
            ->where('business_no', $business_no)
            ->update([
                'operator_remark' => $remark,
                'operator_node' => $task['node_id'],
                'operator' => $this->uid,
                'approval_status' => ApprovalStatusEnum::RETURNED->value,
                'status' => ApplyStatusEnum::RETURNED->value
            ]);
    }

    /**
     * 任务拒绝
     * @param $business_no
     * @param $task
     * @param string $remark
     * @return void
     */
    public function reject($business_no, $task, $remark = ''): void
    {
        $row = $this->model->withoutScope()->where('business_no', $business_no)->find();

        $row->operator_remark = $remark;
        $row->operator_node = $task['node_id'];
        $row->operator = $this->uid;
        $row->approval_status = ApprovalStatusEnum::REJECTED->value;
        $row->status = ApplyStatusEnum::RETURNED->value;

        $row->save();

        if ($row->wx_uid) {
            AppMessage::sendNotice(1, '审核未通过', '您的申请未通过:' . $remark, $row->wx_uid, [
                'description' => '',
                'variable' => [
                    'hzbh' => $row->hzbh,
                    'czr' => $row->czr,
                    'nian' => $row->nian,
                    'qi' => $row->qi,
                ],
            ]);
        }
    }

    /**
     * 任务完成
     * @param $business_no
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function finished($business_no): void
    {
        Db::startTrans();
        try {

            // todo 确保可查询到数据 去掉数据过滤
            $row = $this->model->withoutScope()->where('business_no', $business_no)->find();
            $row->approval_status = ApprovalStatusEnum::FINISHED->value;
            $row->status = ApplyStatusEnum::FINISHED->value;
            $row->save();

            $archive = Archive::create([
                'apply_id' => $row['id'],
                'qc_id' => $row['qc_id'],
                'fylx' => $row['fylx'],
                'sblx' => $row['sblx'],
                'sbqy' => $row['sbqy'],
                'sbtj' => $row['sbtj'],
                'czr' => $row['czr'],
                'sex' => $row['sex'],
                'czrzjhm' => $row['czrzjhm'],
                'czrlxdh' => $row['czrlxdh'],
                'czrgzdw' => $row['czrgzdw'],
                'sb_date' => $row['sb_date'],
                'xq_id' => $row['xq_id'],
                'sbxq' => $row['sbxq'],
                'sbhx' => $row['sbhx'],
                'bzfs' => $row['bzfs'],
                'status' => ArchiveStatusEnum::QUALIFIED->value,
                'hzbh' => $row['hzbh'], // 回执编号
                'zgbh' => $row['zgbh'], // 资格编号
                // 'zg_time' => $row['zg_time'],
                // 'lhbh' => $row['lhbh'],
                // 'lh_time' => $row['lh_time'],
                // 'fc_time' => $row['fc_time'],
                // 'fc_remark' => $row['fc_remark'],

                'admin_id' => $this->uid ?? 0,
                'area_code' => $row['area_code'],
                // todo 业务办理人
                'add_user' => $data['add_user'] ?? ($data['sp_user'] ?? ''),
            ]);

            $family_members = json_decode($row['family_info'], true);
            foreach ($family_members as $key => $family_member) {
                FamilyMember::create([
                    'apply_id' => $row['id'],
                    'archive_id' => $archive['id'],
                    'yzsqrgx' => $family_member['yzsqrgx'] ?? '',
                    'is_gtsqr' => $family_member['is_gtsqr'] ?? '',
                    'name' => $family_member['name'],
                    'zjlb' => $family_member['zjlb'] ?? '',
                    'zjhm' => $family_member['zjhm'] ?? '',
                    'birth' => (isset($family_member['birth']) && $family_member['birth'] != '') ? $family_member['birth'] : null,
                    'sex' => $family_member['sex'] ?? '',
                    'mz' => $family_member['mz'] ?? '',
                    'marriage' => $family_member['marriage'] ?? '',
                    'hjszd' => $family_member['hjszd'] ?? '',
                    'hjszdxq' => $family_member['hjszdxq'] ?? '',
                    'xjzszd' => $family_member['xjzszd'] ?? '',
                    'is_bdhj' => $family_member['is_bdhj'] ?? '',
                    'jznx' => $family_member['jznx'] ?? '',
                    'hjqr_date' => (isset($family_member['hjqr_date']) && $family_member['hjqr_date'] != '') ? $family_member['hjqr_date'] : null,
                    'whcd' => $family_member['whcd'] ?? '',
                    'by_date' => (isset($family_member['by_date']) && $family_member['by_date'] != '') ? $family_member['by_date'] : null,
                    'jyqk' => $family_member['jyqk'] ?? '',
                    'zylx' => $family_member['zylx'] ?? '',
                    'gzdw' => $family_member['gzdw'] ?? '',
                    'gzxz' => $family_member['gzxz'] ?? null,
                    'dwdh' => $family_member['dwdh'] ?? '',
                    'dwdz' => $family_member['dwdz'] ?? '',
                    'bzqd_date' => $family_member['bzqd_date'] ?? null,
                    'jynx' => $family_member['jynx'] ?? '',
                    'ygxz' => $family_member['ygxz'] ?? null,
                    'zcmc' => $family_member['zcmc'] ?? '',
                    'ht_start_date' => (isset($family_member['ht_start_date']) && $family_member['ht_start_date'] != '') ? $family_member['ht_start_date'] : null,
                    'ht_end_date' => (isset($family_member['ht_end_date']) && $family_member['ht_end_date'] != '') ? $family_member['ht_end_date'] : null,
                    'ysr' => $family_member['ysr'] ?? '',
                    'nsr' => $family_member['nsr'] ?? '',
                    'qt_nsr' => $family_member['qt_nsr'] ?? 0,
                    'jtnsr' => $family_member['jtnsr'] ?? '',
                    'rjnsr' => $family_member['rjnsr'] ?? '',
                    'sb_start_date' => (isset($family_member['sb_start_date']) && $family_member['sb_start_date'] != '') ? $family_member['sb_start_date'] : null,
                    'sb_end_date' => (isset($family_member['sb_end_date']) && $family_member['sb_end_date'] != '') ? $family_member['sb_end_date'] : null,
                    'lxdh' => $family_member['lxdh'] ?? '',
                    'jjlxrdh' => $family_member['jjlxrdh'] ?? '',
                    'ssqt' => $family_member['ssqt'] ?? '',
                    'jzzhm' => $family_member['jzzhm'] ?? '',
                    'jzzfzjg' => $family_member['jzzfzjg'] ?? '',
                    'yhkh' => $family_member['yhkh'] ?? '',
                    'is_dbry' => $family_member['is_dbry'] ?? '',
                    'dbzhm' => $family_member['dbzhm'] ?? '',
                    'bz' => $family_member['bz'] ?? '',
                    'cq_state' => $family_member['cq_state'] ?? '',
                    'cqcx_time' => (isset($family_member['cqcx_time']) && $family_member['cqcx_time'] != '') ? $family_member['cqcx_time'] : null,
                    'sfzj_file' => $family_member['sfzj_file'] ?? '',
                    'is_yf' => $family_member['is_yf'] ?? '',
                    'yfxzmj' => $family_member['yfxzmj'] ?? '',
                    'yfrjmj' => $family_member['yfrjmj'] ?? '',
                    'area_code' => $row['area_code'],
                    'admin_id' => $this->uid ?? 0,
                ]);
            }

            (new LeaseLogLogic)->record(LeaseLog::TYPE['SQFSTG'], [
                'apply_id' => $row->id,
                'remark' => '审批通过'
            ]);

            Db::commit();
            if ($row->wx_uid) {
                AppMessage::sendNotice(1, '审核通过', '您的申请已通过', $row->wx_uid, [
                    'description' => '',
                    'variable' => [
                        'hzbh' => $row->hzbh,
                        'czr' => $row->czr,
                        'nian' => $row->nian,
                        'qi' => $row->qi,
                    ],
                ]);
            }
        } catch (\Throwable $e) {
            throw new ApiException($e->getMessage());
        }
    }

    /**
     * 审批任务详情
     * @param $business_no
     * @return array|mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function taskView($business_no): mixed
    {
        // todo 确保可查询到数据 去掉数据过滤
        return $this->model->withoutScope()->where('business_no', $business_no)->find();
    }
}
