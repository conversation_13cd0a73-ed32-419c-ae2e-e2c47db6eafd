<?php

namespace app\controller\backend\logic\ration;

use app\ApiException;
use app\controller\backend\logic\AdminLogicStrategy;
use app\controller\backend\model\ration\Archive;
use app\controller\backend\model\ration\LeaseLog;
use app\controller\backend\model\ration\Period;
use app\enums\ration\ArchiveStatusEnum;
use app\model\Estate;
use think\facade\Db;
use think\Model;

class ArchiveLogic extends AdminLogicStrategy
{
    public function __construct()
    {
        $this->model = new Archive();

        parent::__construct($this->model);
    }

    public function generateNewZgbh($data): string
    {
        // 查询最大的zgbh
        $maxZgbh = $this->model->where('xq_id', '=', $data['xq_id'])->max('zgbh');

        if ($maxZgbh) {
            $zgbh = $maxZgbh + 1;
        } else {
            // 查询小区的编号
            $xq_fydm = Estate::where('id', '=', $data['xq_id'])->value('fydm');

            $zgbh = date("Y") . $xq_fydm . "001";
        }

        return $zgbh;
    }

    public function setZgbh(array $data): bool
    {
        // todo: 可能重复+位数不够
        $archive = Archive::find($data['id']);
        if (!$archive) {
            throw new \Exception('档案记录不存在');
        }

        $archive->zgbh = $data['zgbh'];
        $archive->zg_time = $data['zg_time'];
        $archive->status = ArchiveStatusEnum::QUALIFIED_NUMBER->value;
        $archive->save();

        (new LeaseLogLogic)->record(LeaseLog::TYPE['SQDASCZGZH'], [
            'apply_id' => $archive->apply_id,
            'archive_id' => $archive->id,
        ]);

        return true;
    }

    public function getNewZgbhBatch($data)
    {
        $qici = Period::where('status', '=', '1')->find();
        if (!$qici) {
            throw new ApiException('当前没有可用的期次');
        }

        $xq_fydm = Estate::cache()->column('id,fydm', 'id');

        $maxZgbhs = Archive::alias('a')
            ->withoutGlobalScope()
            ->where('qc_id', '=', $qici->id)
            ->field('a.xq_id,max(a.zgbh) as zgbh_max,a.sbxq')
            ->join(['period' => 'p'], 'p.id=a.qc_id')
            ->group('a.xq_id')
            ->column('a.xq_id,max(a.zgbh) as zgbh_max,a.sbxq', 'a.xq_id');

        $countZgbh = $this->model->field('xq_id,count(xq_id) as xq_sq_count,sbxq')
            // ->where('xq_id', '=', $data['xq_id'])
            ->where('zgbh', 'null')
            ->where('status', '=', ArchiveStatusEnum::QUALIFIED->value)
            ->group('xq_id')
            ->select();

        if (!$countZgbh->isEmpty()) {
            $result = [];
            foreach ($countZgbh as $item) {
                $xq_id = $item['xq_id'];
                $xq_sq_count = $item['xq_sq_count'];
                $sbxq = $item['sbxq'];

                if (isset($maxZgbhs[$xq_id]) && !empty($maxZgbhs[$xq_id]['zgbh_max'])) {
                    $last_zgbh = $maxZgbhs[$xq_id]['zgbh_max'];
                } else {
                    $last_zgbh = date("Y") . $xq_fydm[$xq_id]['fydm'] . "000";
                }

                $result[] = [
                    'xq_id' => $xq_id,
                    'sbxq' => $sbxq,
                    'xq_sq_count' => $xq_sq_count,
                    'last_zgbh' => $last_zgbh,
                    'new_zgbh_start' => $last_zgbh + 1,
                    'new_zgbh_end' => $last_zgbh + $item['xq_sq_count'],
                ];
            }
            return $result;
        } else {
            return [];
        }

    }

    public function setZgbhBatch(array $data): bool
    {
        Db::startTrans(); // 启动事务
        try {
            // todo: 可能重复+位数不够
            $archives = Archive::where('xq_id', '=', $data['xq_id'])
                ->where('zgbh', 'null')
                ->where('status', '=', ArchiveStatusEnum::QUALIFIED->value)
                ->order(['sbhx' => 'asc', 'id' => 'asc'])
                ->select();

            if (!$archives->isEmpty()) {
                $i = 0;
                foreach ($archives as $key => $archive) {
                    $archive->zgbh = $data['zgbh'] + $i;
                    $archive->zg_time = $data['zg_time'];
                    $archive->status = ArchiveStatusEnum::QUALIFIED_NUMBER->value;
                    $archive->save();

                    (new LeaseLogLogic)->record(LeaseLog::TYPE['SQDASCZGZH'], [
                        'apply_id' => $archive->apply_id,
                        'archive_id' => $archive->id,
                    ]);

                    $i++;
                }
            }
            Db::commit(); // 提交事务
        } catch (\Exception $e) {
            Db::rollback(); // 回滚事务
            throw $e;
        }
        return true;
    }

    public function family(int $id): ?Model
    {
        $where = [];

        return $this->model->where($where)->with('famlilyMember')->hidden(['delete_time'])->find($id);
    }

    public function gongshi(array $data): string
    {
        // 开始事务
        Db::startTrans();
        try {
            $valid_archive = Archive::where('id', 'in', $data['ids'])->where('status', '<>', ArchiveStatusEnum::QUALIFIED_NUMBER->value)->find();
            if ($valid_archive) {
                throw new ApiException('存在无法提交公示的记录，如：' . $valid_archive->czr);
            }

            // 获取要公示的档案记录
            $archives = Archive::whereIn('id', $data['ids'])->select();

            if ($archives->isEmpty()) {
                throw new \Exception('未找到数据');
            }

            // 更新档案记录的公示信息
            foreach ($archives as $archive) {
                $archive->gs_kssj = $data['gs_kssj'];
                $archive->gs_jssj = $data['gs_jssj'];
                $archive->status = ArchiveStatusEnum::QUALIFIED_PUBLICITY->value;
                $archive->save();

                (new LeaseLogLogic)->record(LeaseLog::TYPE['SQDATJGS'], [
                    'apply_id' => $archive->apply_id,
                    'archive_id' => $archive->id
                ]);
            }

            // 提交事务
            Db::commit();

            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            throw $e;
        }
    }

    public function cancelGongshi(array $data): string
    {
        // 开始事务
        Db::startTrans();
        try {
            $valid_archive = Archive::where('id', 'in', $data['ids'])->where('status', '<>', ArchiveStatusEnum::QUALIFIED_PUBLICITY->value)->find();
            if ($valid_archive) {
                throw new ApiException('查到不是' . Archive::STATUS['ZGGS'][1] . '的记录，如：' . $valid_archive->czr);
            }

            // 获取要公示的档案记录
            $archives = Archive::whereIn('id', $data['ids'])->select();

            if ($archives->isEmpty()) {
                throw new \Exception('未找到数据');
            }

            // 更新档案记录的公示信息
            foreach ($archives as $archive) {
                $archive->gs_kssj = null;
                $archive->gs_jssj = null;
                $archive->status = ArchiveStatusEnum::QUALIFIED_NUMBER->value;
                $archive->save();

                (new LeaseLogLogic)->record(LeaseLog::TYPE['SQDAQXGS'], [
                    'apply_id' => $archive->apply_id,
                    'archive_id' => $archive->id
                ]);
            }

            // 提交事务
            Db::commit();

            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            throw $e;
        }
    }
}
