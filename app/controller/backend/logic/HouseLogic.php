<?php

namespace app\controller\backend\logic;

use app\model\House;
use think\facade\Db;

class HouseLogic extends AdminLogicStrategy
{
    public function __construct()
    {
        $this->model = new House();

        parent::__construct($this->model);
    }

    public function import($data)
    {
        Db::startTrans(); // 启动事务
        try {
            foreach ($data as $house) {
                $this->model->create($house);
            }
            Db::commit(); // 提交事务
        } catch (\Exception $e) {
            Db::rollback(); // 回滚事务
            throw $e;
        }
    }

    /**
     * 批量导入房源数据（优化版本）
     * @param array $data
     * @return bool
     * @throws \Exception
     */
    public function batchImport(array $data): bool
    {
        if (empty($data)) {
            return true;
        }

        Db::startTrans(); // 启动事务
        try {
            // 使用批量插入，每次插入100条记录
            $batchSize = 100;
            $chunks = array_chunk($data, $batchSize);

            foreach ($chunks as $chunk) {
                // 添加时间戳
                $insertData = [];
                $currentTime = date('Y-m-d H:i:s');

                foreach ($chunk as $item) {
                    $item['create_time'] = $currentTime;
                    $item['update_time'] = $currentTime;
                    $insertData[] = $item;
                }

                // 批量插入
                $this->model->insertAll($insertData);
            }

            Db::commit(); // 提交事务
            return true;
        } catch (\Exception $e) {
            Db::rollback(); // 回滚事务
            throw $e;
        }
    }

    public function del(int|string|array $id): bool
    {
        $where[] = ['id', 'in', $id];

        $result = $this->model->where($where)->field('id')->select();

        if (!$result->isEmpty()) {
            foreach ($result as $item) {
                // todo 这里可以添加一些逻辑来判断是否可以删除，比如是否有相关联的记录等
                if ($item->status == 0) {
                    $item->delete();
                }
            }
        }

        return true;
    }
}
