<?php

namespace app\controller\backend\validate\ration;

use app\enums\ration\ApplyStatusEnum;
use think\Validate;
use app\controller\backend\model\ration\Apply;
use Jxlwqq\IdValidator\IdValidator;

class ApplyValidate extends Validate
{
    protected $failException = true;

    protected $rule = [
        'id|ID' => 'require|integer',
        'qc_id|期次编号' => 'require|max:20',
        'nian|期次-年' => 'max:255',
        'qi|期次-期' => 'max:255',
        'sblx|申报类型' => 'require|integer|in:1,2,3',
        'sbqy|申报区域' => 'integer',
        'fylx|房源类型' => 'require|integer|in:3,4,99',
        'sbtj|申报途径' => 'require|integer|in:1,2',
        'czr|承租人' => 'require|max:50',
        'sex|性别' => 'require|integer|in:0,1,2',
        'czrzjhm|承租人证件号码' => 'require|max:18|isIdCard',
        'czrlxdh|承租人联系电话' => 'require|max:11',
        'czrgzdw|承租人工作单位' => 'require|max:100',

        'sqr_info|申请人信息' => 'max:65535',
        'family_info|家庭成员' => 'max:65535', //TODO: 身份证验证
        'other_info|申请其他信息' => 'max:512',

        'sb_date|申报日期' => 'require|date',
        'xq_id|小区ID' => 'require|integer',
        'sbxq|申报小区' => 'require|max:100',
        'sbhx|申报户型' => 'require|integer|in:1,2,3,4,5,6,7,8,99',
        'bzfs|保障方式' => 'require|integer|in:1',
        'status|状态' => 'require|integer|in:0,100,105,110,200,320',
        'add_user|添加人' => 'max:32',
        'sl_date|受理日期' => 'date',
        'hzbh|回执编号' => 'max:32',
        'bz|备注' => 'max:255',
        'zm_files|证明文件' => 'max:512',
    ];

    /**
     * 验证提示信息
     * @var array
     */
    protected $message = [];

    /**
     * 字段描述
     */
    protected $field = [];

    /**
     * 验证场景
     */
    protected $scene = [
        'add' => ['qc_id', 'nian', 'qi', 'sblx', 'sbqy', 'fylx', 'sbtj', 'czr', 'sex', 'czrzjhm', 'czrlxdh', 'czrgzdw', 'sqr_info', 'family_info', 'other_info', 'sb_date', 'xq_id', 'sbxq', 'sbhx', 'bzfs', 'status', 'add_user', 'bz', 'zm_files'],
        'edit' => ['id', 'qc_id', 'nian', 'qi', 'sblx', 'sbqy', 'fylx', 'sbtj', 'czr', 'sex', 'czrzjhm', 'czrlxdh', 'czrgzdw', 'sqr_info', 'family_info', 'other_info', 'sb_date', 'xq_id', 'sbxq', 'sbhx', 'bzfs', 'status', 'add_user', 'bz', 'zm_files'],
        'del' => ['id'],
        'tjsp' => ['id'],
        'sp1' => ['id'],
        'spEnd' => ['id'],
    ];

    // TODO 加强验证

    public function sceneDel()
    {
        return $this->only([
            'id',
        ])->append('id', 'canDel');
    }

    public function canDel($value, $rule, $data)
    {
        $apply = Apply::where('id', $data['id'])->find();
        if (!$apply) {
            return '申报记录不存在';
        }
        if (!in_array($apply['status'], [ApplyStatusEnum::DRAFT->value])) {
            return '无法删除，申报状态不是' . ApplyStatusEnum::DRAFT->label();
        }

        return true;
    }

    public function sceneCheckApply()
    {
        return $this->only([
            'qc_id',
            'czrzjhm',
            'xq_id',
            'sbhx',
        ]);
    }

    public function sceneStage()
    {
        return $this->only([
            // 'id',
            'qc_id',
            'nian',
            'qi',
            'sblx',
            'sbqy',
            'fylx',
            'sbtj',
            'czr',
            'sex',
            'czrzjhm',
            'czrlxdh',
            'czrgzdw',
            'sqr_info',
            'family_info',
            'other_info',
            'sb_date',
            'xq_id',
            'sbxq',
            'sbhx',
            'bzfs',
            // 'status',
            // 'add_user',
            'sl_date',
            // 'hzbh',
            'bz',
            'zm_files'
        ])
            ->remove('status', 'require');
    }

    public function canStage($value, $rule, $data)
    {
        $apply = Apply::where('id', $data['id'])->find();
        if (!$apply) {
            return '申报记录不存在';
        }
        if (!in_array($apply['status'], [ApplyStatusEnum::DRAFT->value])) {
            return '无法操作，申报状态不是' . ApplyStatusEnum::DRAFT->label();
        }
        return true;
    }

    public function scenePrecheck()
    {
        return $this->only(['id', 'sl_date'])
            ->append('sl_date', 'require')
            ->append('id', 'canPrecheck');
    }

    protected function canPrecheck($value, $rule, $data)
    {
        // $apply = Apply::where('id', $value)->find();
        // if (!$apply) {
        //     return '申报记录不存在';
        // }
        // if ($apply['status'] != ApplyStatusEnum::DRAFT->value) {
        //     return '无法操作，申报状态不是草稿';
        // }
        return true;
    }


    public function sceneRecheck()
    {
        // TODO
        return $this->only([
            ''
        ]);
    }

    protected function isIdCard($value, $rule, $data)
    {
        $validator = new IdValidator();
        if (!$validator->isValid($value)) {
            return '请输入正确的身份证号码';
        }

        return true;
    }
}
