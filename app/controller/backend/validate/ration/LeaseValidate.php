<?php

namespace app\controller\backend\validate\ration;

use think\Validate;
use app\controller\backend\model\ration\Lease;
use app\controller\backend\validate\ration\ext\YaohaoInfoItemValidate;
use app\controller\backend\model\ration\Archive;

class LeaseValidate extends Validate
{
    protected $failException = true;

    protected $rule = [
        'id|ID' => 'require|integer',
        'archive_id|档案编号' => 'require|integer',
        'xq_id|小区ID' => 'require|integer',
        'xq_mc|小区名称' => 'max:100',
        'fw_id|房屋ID' => 'require|integer',
        'mph|门牌号' => 'max:100',
        'type|配租类型' => 'require|integer',

        'pz_user' => 'require|max:64',
        'pz_time' => 'require|date',

        'ids' => 'require',

        'yaohao_info' => 'require|canDrYaohao',
    ];

    /**
     * 验证提示信息
     * @var array
     */
    protected $message = [];

    /**
     * 字段描述
     */
    protected $field = [];

    /**
     * 验证场景
     */
    protected $scene = [
        // TODO: 场景验证
        'add' => [
            'archive_id',
            'xq_id',
            'xq_mc',
            'fw_id',
            'mph',
            'type',
        ],
    ];


    // drYaohao场景
    public function sceneDrYaohao()
    {
        return $this->only(['yaohao_info', 'pz_user', 'pz_time']);
    }


    protected function canDrYaohao($value, $rule, $data = [])
    {
        $t_row_key = 0;
        foreach ($data['yaohao_info'] as $dr_val) {
            $t_row_key++;
            try {
                if (isset($dr_val['fw_id']) && $dr_val['fw_id'] > 0) {
                    validate(YaohaoInfoItemValidate::class)->scene('daoru')->check($dr_val);
                } else if (isset($dr_val['fw_id']) && $dr_val['fw_id'] == 0) {
                    validate(YaohaoInfoItemValidate::class)->scene('lunhou')->check($dr_val);
                } else {
                    return "第" . $t_row_key . "条数据：房屋ID不正确-摇中>0或轮候=0";
                }

                // 验证是否公示完毕状态
                $archive = Archive::where('zgbh', $dr_val['zgbh'])->find();
                if (!$archive) {
                    return "第" . $t_row_key . "条数据：资格编号 " . $dr_val['zgbh'] . " 不存在";
                }
                if ($archive->status != Archive::STATUS['ZGGSJS'][0]) {
                    return "第" . $t_row_key . "条数据：资格编号 " . $dr_val['zgbh'] . " 未公示完毕，不能导入摇号信息";
                }

            } catch (\Throwable $e) {
                return "第" . $t_row_key . "条数据：" . $e->getMessage();
            }
        }

        $zgbh_list = array_column($data['yaohao_info'], 'zgbh');
        if (count($zgbh_list) != count(array_unique($zgbh_list))) {
            return "存在重复的资格编号";
        }
        // $lease_list = Lease::where('zgbh', 'in', $zgbh_list)->select();
        // if (!$lease_list->isEmpty()) {
        //     return "存在已导入的资格编号:" . implode(',', $lease_list->column('zgbh'));
        // }

        $fw_id_list = array_column($data['yaohao_info'], 'fw_id');
        if (count($fw_id_list) != count(array_unique($fw_id_list))) {
            return "存在重复的房屋ID";
        }
        // 房屋是否存在，并且是空置房


        // TODO: 进一步验证 是否已导入，其他必要信息 房屋是否满足条件，申请档案是否满足条件 等等

        return true;
    }

    public function sceneRuzhu()
    {
        return $this->only(['ids']);
    }

    /**
     * 是否已存在
     */
    protected function isExist($value, $rule, $data = [])
    {
        $mainModel = Lease::field('id');
        if (isset($data['id'])) {
            $mainModel->where('id', '<>', $data['id']);
        }
        // TODO: 验证是否已存在
        // $mainModel->where('nian', $data['nian'])
        //      ->where('qi', $data['qi']);

        $main = $mainModel->find();
        if ($main) {
            return '数据已存在';
        }

        return true;
    }
}
