<?php

namespace app\controller\backend\validate\ration;

use think\Validate;
use app\controller\backend\model\ration\Period;
use app\model\House;

class PeriodValidate extends Validate
{
    protected $failException = true;

    protected $rule = [
        'id|ID' => 'require|integer',
        'nian|年份' => 'require|integer|between:1970,2099|isExist',
        'qi|期次' => 'require|integer|between:1,12',
        'start_time|开始日期' => 'require|date|lt:end_time',
        'end_time|结束日期' => 'require|date',
        'status|状态' => 'require|in:0,1|onlyOneOpen',
        'remark|备注' => 'max:255',
        'fw_ids|房源ID列表' => 'max:65535|canChooseFw',
        'config|配置' => 'max:65535',
    ];

    /**
     * 验证提示信息
     * @var array
     */
    protected $message = [];

    /**
     * 字段描述
     */
    protected $field = [];

    /**
     * 验证场景
     */
    protected $scene = [
        'add' => ['nian', 'qi', 'start_time', 'end_time', 'status', 'remark', 'fw_ids', 'config'],
        'edit' => ['id', 'nian', 'qi', 'start_time', 'end_time', 'status', 'remark', 'fw_ids', 'config'],
        'del' => ['id'],
        'status' => ['id', 'status'],
    ];

    protected function canChooseFw($value, $rule, $data = []): bool|string
    {
        try {
            if (isset($data['id']) && $data['id'] != '') {
                $qici = Period::where('id', $data['id'])->find();
                if (!$qici) {
                    return '期次不存在';
                }

                $t_old_fw_ids = [];
                if (!empty($qici['fw_ids'])) {
                    $old_fw_ids = $qici['fw_ids'];
                    foreach ($old_fw_ids as $fwids) {
                        $t_old_fw_ids = array_merge($t_old_fw_ids, $fwids);
                    }
                }

                $t_new_fw_ids = [];
                foreach ($data['fw_ids'] as $fwids) {
                    $t_new_fw_ids = array_merge($t_new_fw_ids, $fwids);
                }

                $t_diff_fw_ids = array_diff($t_new_fw_ids, $t_old_fw_ids);
                $house = House::where('id', 'in', $t_diff_fw_ids)->where('fwzt', '<>', House::FWZT['KZ'][0])->find();
                if ($house) {
                    return "{$house['xqmc']}{$house['ldmc']}#{$house['dy']}-{$house['lc']}-{$house['xh']}" . " 房源状态不是" . House::FWZT['KZ'][1];
                }

            } else {
                foreach ($data['fw_ids'] as $fwids) {
                    $house = House::where('id', 'in', $fwids)->where('fwzt', '<>', House::FWZT['KZ'][0])->find();
                    if ($house) {
                        return "{$house['xqmc']}{$house['ldmc']}#{$house['dy']}-{$house['lc']}-{$house['xh']}" . " 房源状态不是" . House::FWZT['KZ'][1];
                    }
                }
            }
        } catch (\Throwable $th) {
            // throw $th;
            return 'fw_ids参数错误:' . $th->getMessage();
        }

        return true;
    }

    /**
     * 是否已存在
     */
    protected function isExist($value, $rule, $data = []): bool|string
    {
        $mainModel = Period::field('id');
        $mainModel->where('area_code', $data['area_code']);
        if (isset($data['id'])) {
            $mainModel->where('id', '<>', $data['id']);
        }

        $mainModel->where('nian', $data['nian'])->where('qi', $data['qi']);

        $main = $mainModel->find();
        if ($main) {
            return '期次已存在';
        }

        return true;
    }

    protected function onlyOneOpen($value, $rule, $data = []): bool|string
    {
        if ($value == '1') {
            $m = Period::where(['status' => '1', 'area_code' => $data['area_code']]);

            if (isset($data['id'])) {
                $m->where('id', '<>', $data['id']);
            }
            $main = $m->find();
            if ($main) {
                return '只能有一个摇号期次处于开启状态';
            }
        }

        return true;
    }
}
