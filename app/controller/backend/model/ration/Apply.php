<?php

namespace app\controller\backend\model\ration;

use app\BaseModel;
use think\model\concern\SoftDelete;
use app\traits\ManageAreaScopeTrait;

class Apply extends BaseModel
{
    use SoftDelete, ManageAreaScopeTrait;

    protected bool $enableDataScope = true;
    protected $globalScope = ['ManageArea'];

    protected $json = ['sp_info', 'zm_files'];
    protected $jsonAssoc = true;

    // 申报途径
    public const SBTJ = [
        'ONLINE' => [1, '线上自助'],
        'OFFLINE' => [2, '线下窗口'],
    ];


    public static function onBeforeWrite($data)
    {
        if (isset($data->sqr_info)) {
            $data->sqr_info = json_encode($data->sqr_info, JSON_UNESCAPED_UNICODE);
        }
        if (isset($data->family_info)) {
            if ($data->family_info == null || $data->family_info == "") {
                $data->family_info = [];
            }
            $data->family_info = json_encode($data->family_info, JSON_UNESCAPED_UNICODE);
        }
        if (isset($data->other_info)) {
            $data->other_info = json_encode($data->other_info, JSON_UNESCAPED_UNICODE);
        }
    }

    public static function onAfterRead($data)
    {
        if (isset($data->sqr_info)) {
            $data->sqr_info = json_decode($data->sqr_info, true);
        }
        if (isset($data->family_info)) {
            $data->family_info = json_decode($data->family_info, true);
        }
        if (isset($data->other_info)) {
            $data->other_info = json_decode($data->other_info, true);
        }
    }
}
