<?php

namespace app\controller\backend\model\ration;

use app\BaseModel;
use app\controller\backend\validate\ration\LeaseValidate;
use app\model\House;
use think\facade\Db;
use think\model\concern\SoftDelete;
use app\traits\ManageAreaScopeTrait;
use think\model\relation\BelongsTo;
use think\model\relation\HasOne;

class Lease extends BaseModel
{
    use SoftDelete, ManageAreaScopeTrait;

    protected bool $enableDataScope = true;
    protected $globalScope = ['ManageArea'];

    public function archive(): HasOne
    {
        return $this->hasOne(Archive::class, 'id', 'archive_id');
    }

    public function house(): HasOne
    {
        return $this->hasOne(House::class, 'id', 'fw_id');
    }

    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'id', 'lease_id');
    }

    public function renew(): BelongsTo
    {
        return $this->belongsTo(ContractRenewApply::class, 'id', 'lease_id');
    }

    /**
     * Create a new lease record.
     * @throws \Exception
     */
    public static function createLeaseRecord(array $leaseData): Lease
    {
        Db::startTrans();
        try {
            validate(LeaseValidate::class)->scene('add')->check($leaseData);
            $result = Lease::create($leaseData);

            // todo 可做统一日志处理

            Db::commit();
            return $result;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

}
