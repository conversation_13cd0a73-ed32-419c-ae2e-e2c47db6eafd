<?php

namespace app\controller\backend\model\ration;

use app\BaseModel;
use think\model\concern\SoftDelete;
use app\traits\ManageAreaScopeTrait;

class ContractRenewApply extends BaseModel
{
    use SoftDelete, ManageAreaScopeTrait;

    protected bool $enableDataScope = true;
    protected $globalScope = ['ManageArea'];

    // protected $json = [''];
    // protected $jsonAssoc = true;

    public const STATUS = [
        'DSP' => [0, '待审批'],
        'TG' => [1, '通过'],
        'JJ' => [2, '拒绝'],
        'CX' => [3, '撤销']
    ];

    public function contract(): \think\model\relation\HasOne
    {
        return $this->hasOne(Contract::class, 'id', 'contract_id');
    }

    public function newContract(): \think\model\relation\HasOne
    {
        return $this->hasOne(Contract::class, 'id', 'new_contract_id');
    }

    public function lease(): \think\model\relation\HasOne
    {
        return $this->hasOne(Lease::class, 'id', 'lease_id');
    }

    public function newLease(): \think\model\relation\HasOne
    {
        return $this->hasOne(Lease::class, 'id', 'new_lease_id');
    }
}
