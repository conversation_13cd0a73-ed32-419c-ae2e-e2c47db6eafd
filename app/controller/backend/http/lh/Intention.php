<?php

namespace app\controller\backend\http\lh;

use app\controller\backend\validate\lh\IntentionValidate;
use think\App;
use think\exception\ValidateException;
use think\response\Json;
use app\common\controller\Backend;
use app\controller\backend\logic\lh\IntentionLogic;

class Intention extends Backend
{
    protected IntentionLogic $logic;

    public function __construct(APP $app, IntentionLogic $logic)
    {
        parent::__construct($app);

        $this->logic = $logic;
        $this->logic->setUid($this->auth->id);
    }

    public function index(): Json
    {
        $where = [];
        $limit = $this->request->param('limit/d', 15);
        $params = $this->request->param();
        isset($params['intention_type']) && is_numeric($params['intention_type']) && $where[] = ['intention_type', '=', $params['intention_type']];

        $data = $this->logic->getList($where, 'Intention.*', '', $limit);

        return $this->success('success', $data);
    }


    public function detail(): Json
    {
        $data = $this->request->only([
            'id'
        ]);
        $data = $this->logic->detail($data['id']);

        return $this->success('success', $data);
    }

    public function del(): Json
    {
        $data = $this->request->only([
            'id'
        ]);

        try {
            validate(IntentionValidate::class)->scene('del')->check($data);
            $this->logic->del($data);
            return $this->success('删除成功');
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }

    public function batchDel(): Json
    {
        $data = $this->request->only([
            'ids'
        ]);

        try {
            validate(IntentionValidate::class)->scene('batchDel')->check($data);
            $this->logic->del($data['ids']);
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }

        return $this->success('删除成功');
    }

    public function status(): Json
    {
        $data = $this->request->only([
            'id',
            'status'
        ]);

        try {
            validate(IntentionValidate::class)->scene('status')->check($data);
            $this->logic->edit($data);
            return $this->success('更新成功');
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }
}
