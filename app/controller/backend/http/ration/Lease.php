<?php

namespace app\controller\backend\http\ration;

use app\common\controller\Backend;
use app\controller\backend\logic\ration\LeaseLogic;
use app\controller\backend\validate\ration\LeaseValidate;
use app\enums\ration\LeaseStatusEnum;
use think\App;
use think\exception\ValidateException;
use think\response\Json;

class Lease extends Backend
{
    protected LeaseLogic $logic;

    public function __construct(APP $app, LeaseLogic $logic)
    {
        parent::__construct($app);

        $this->logic = $logic;
        $this->logic->setUid($this->auth->id);
    }

    public function index(): Json
    {
        $where = [];
        $limit = $this->request->param('limit/d', 15);
        $params = $this->request->param();

        $where[] = ['Lease.status', 'in', [
            LeaseStatusEnum::PENDING->value,
            LeaseStatusEnum::SIGNED->value,
        ]];

        $data = $this->logic->setBeforeGetList(function ($query) {
            return $query->with(['archive', 'contract', 'house']);
        })->getList($where, 'Lease.*', 'id desc', $limit);

        return $this->success('success', $data);
    }

    public function record(): Json
    {
        $where = [];
        $limit = $this->request->param('limit/d', 15);
        $archive_id = $this->request->param('archive_id/d');

        $where[] = ['archive_id', '=', $archive_id];
        $where[] = ['Lease.status', '>=', 0];

        $data = $this->logic->setBeforeGetList(function ($query) {
            return $query->with(['archive', 'contract', 'house']);
        })->getList($where, 'Lease.*', 'id desc', $limit);

        return $this->success('success', $data);
    }

    public function detail(): Json
    {
        $data = $this->request->only([
            'id'
        ]);
        $data = $this->logic->detail($data['id']);

        return $this->success('success', $data);
    }

    public function drYaohao(): Json
    {
        $data = $this->request->only([
            'yaohao_info'
        ]);

        $data['pz_user'] = $this->auth->username;
        $data['pz_time'] = date('Y-m-d H:i:s');
        $data['area_code'] = $this->auth->dept->area_code;

        try {
            // validate(LeaseValidate::class)->scene('drYaohao')->check($data);
            $this->logic->importLotteryResults($data);
            return $this->success('导入成功');
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }

    public function ruzhu(): Json
    {
        $data = $this->request->only([
            'ids'
        ]);
        $data['op_user'] = $this->auth->username;

        try {
            validate(LeaseValidate::class)->scene('ruzhu')->check($data);
            $this->logic->ruzhu($data);
            return $this->success('确定入住成功');
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }


}
