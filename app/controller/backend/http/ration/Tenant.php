<?php

namespace app\controller\backend\http\ration;

use app\common\controller\Backend;
use app\controller\backend\logic\ration\LeaseLogic;
use app\enums\ration\LeaseStatusEnum;
use think\App;
use think\db\exception\DbException;
use think\response\Json;

class Tenant extends Backend
{
    protected LeaseLogic $logic;

    public function __construct(APP $app, LeaseLogic $logic)
    {
        parent::__construct($app);

        $this->logic = $logic;
        $this->logic->setUid($this->auth->id);
    }

    /**
     * 获取租户列表
     * @throws DbException
     */
    public function index(): Json
    {
        $where = [];
        $limit = $this->request->param('limit/d', 15);
        $params = $this->request->param();

        isset($params['czr']) && $params['czr'] != '' && $where[] = ['archive.czr', 'like', "%{$params['czr']}%"];
        isset($params['xq_id']) && $params['xq_id'] != '' && $where[] = ['id', '=', $params['xq_id']];

        $where[] = ['status', 'in', [
            LeaseStatusEnum::CHECKED_IN->value,
            LeaseStatusEnum::CHECKOUT->value,
        ]];

        $data = $this->logic->setBeforeGetList(function ($query) {
            return $query->with(['archive', 'contract']);
        })->getList($where, '*', 'id desc', $limit);

        return $this->success('success', $data);
    }

    /**
     * 退房前预计算
     * @throws \Exception
     */
    public function beforeCheckout(): Json
    {
        $params = $this->request->only([
            'contract_id',
            'lease_id',
            'checkout_date'
        ]);

        $result = $this->logic->beforeCheckout($params);

        return $this->success('获取成功', $result);
    }

    /**
     * 退房操作
     * @throws \Exception
     */
    public function checkout(): Json
    {
        $params = $this->request->param();
        $result = $this->logic->checkout($params);

        return $this->success('提交成功', $result);
    }

    /**
     * 退房完成
     * @throws \Exception
     */
    public function checkoutCompleted(): Json
    {
        $params = $this->request->param();
        $this->logic->checkoutCompleted($params);

        return $this->success('操作成功');
    }

}
