<?php

namespace app\controller\backend\http\ration;

use app\enums\ration\ApplyStatusEnum;
use think\App;
use think\exception\ValidateException;
use think\response\Json;
use app\common\controller\Backend;

use app\controller\backend\validate\ration\ApplyValidate;
use app\controller\backend\validate\ration\FamilyMemberValidate;
use app\controller\backend\logic\ration\ApplyLogic;
use app\controller\backend\model\ration\Apply as ApplyModel;

class Apply extends Backend
{
    protected ApplyLogic $logic;

    public function __construct(APP $app, ApplyLogic $logic)
    {
        parent::__construct($app);

        $this->logic = $logic;
        $this->logic->setUid($this->auth->id);
    }

    public function index(): Json
    {
        $where = [];
        $limit = $this->request->param('limit/d', 15);
        $params = $this->request->param();

        isset($params['qc_id']) && $params['qc_id'] != '' && $where[] = ['qc_id', '=', $params['qc_id']];
        isset($params['nian']) && $params['nian'] != '' && $where[] = ['nian', '=', $params['nian']];
        isset($params['qi']) && $params['qi'] != '' && $where[] = ['qi', '=', $params['qi']];
        isset($params['sblx']) && $params['sblx'] != '' && $where[] = ['sblx', '=', $params['sblx']];
        isset($params['fylx']) && $params['fylx'] != '' && $where[] = ['fylx', '=', $params['fylx']];
        isset($params['sbtj']) && $params['sbtj'] != '' && $where[] = ['sbtj', '=', $params['sbtj']];
        isset($params['xq_id']) && $params['xq_id'] != '' && $where[] = ['xq_id', '=', $params['xq_id']];
        isset($params['xqmc']) && $params['xqmc'] != '' && $where[] = ['xqmc', 'like', "%{$params['xqmc']}%"];
        isset($params['czr']) && $params['czr'] != '' && $where[] = ['czr', 'like', "%{$params['czr']}%"];
        isset($params['czrzjhm']) && $params['czrzjhm'] != '' && $where[] = ['czrzjhm', 'like', "%{$params['czrzjhm']}%"];
        isset($params['status']) && is_numeric($params['status']) && $where[] = ['status', '=', $params['status']];

        $where[] = ['status', '<>', ApplyStatusEnum::DRAFT->value];
        $where[] = ['status', '<', ApplyStatusEnum::FINISHED->value];

        $data = $this->logic->getList($where, '*', 'id desc', $limit);

        return $this->success('success', $data);
    }

    public function add(): Json
    {
        $data = $this->request->only([
            'qc_id',
            'nian',
            'qi',
            'sblx',
            'sbqy',
            'fylx',
            'czr',
            'sex',
            'czrzjhm',
            'czrlxdh',
            'czrgzdw',
            'sqr_info',
            'family_info',
            'other_info',
            'sb_date',
            'xq_id',
            'sbxq',
            'sbhx',
            'bzfs',
            'bz',
            'zm_files',
            // 'sl_date',
            // 'hzbh'
        ]);

        $data['status'] = ApplyStatusEnum::SUBMITTED->value;
        $data['sbtj'] = ApplyModel::SBTJ['OFFLINE'][0];
        $data['add_user'] = $this->auth->username;

        try {
            validate(FamilyMemberValidate::class)->scene('PreCheck')->check($data['sqr_info']);
        } catch (ValidateException $e) {
            return $this->error('申请人信息：' . $e->getMessage());
        }

        foreach ($data['family_info'] as $item) {
            try {
                validate(FamilyMemberValidate::class)->scene('PreCheck')->check($item);
            } catch (ValidateException $e) {
                return $this->error('家庭成员信息：' . ($item['name'] ?? '') . ' ' . $e->getMessage());
            }
        }

        try {
            validate(ApplyValidate::class)->scene('add')->check($data);
            $result = $this->logic->add($data);
            return $this->success('添加成功', $result);
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }

    public function edit(): Json
    {
        $data = $this->request->only([
            'id',
            'qc_id',
            'nian',
            'qi',
            'sblx',
            'sbqy',
            'fylx',
            'sbtj',
            'czr',
            'sex',
            'czrzjhm',
            'czrlxdh',
            'czrgzdw',
            'sqr_info',
            'family_info',
            'other_info',
            'sb_date',
            'xq_id',
            'sbxq',
            'sbhx',
            'bzfs',
            'status',
            // 'add_user',
            'sl_date',
            // 'hzbh',
            'bz',
            'zm_files'
        ]);

        $data['status'] = ApplyStatusEnum::SUBMITTED->value;

        try {
            validate(ApplyValidate::class)->scene('edit')->check($data);
            $this->logic->edit($data);
            return $this->success('更新成功');
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }

    public function detail(): Json
    {
        $data = $this->request->only([
            'id'
        ]);
        $data = $this->logic->detail($data['id']);

        return $this->success('success', $data);
    }

    public function del(): Json
    {
        $data = $this->request->only([
            'id'
        ]);

        try {
            validate(ApplyValidate::class)->scene('del')->check($data);
            $this->logic->del($data);
            return $this->success('删除成功');
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }

    public function checkApply(): Json
    {
        $data = $this->request->only([
            'id' => '',
            'qc_id',
            'czrzjhm',
            'xq_id',
            'sbhx',
        ]);

        try {
            validate(ApplyValidate::class)->scene('checkApply')->check($data);
            $re_data = $this->logic->checkApply($data);

            if ($re_data !== true) {
                return $this->error($re_data);
            } else {
                return $this->success('通过检查');
            }
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }

    public function stage(): Json
    {
        try {
            $check_apply = $this->checkApply()->getData();
            if ($check_apply['code'] != 200) {
                return $this->error($check_apply['msg']);
            }
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }

        $data = $this->request->only([
            'id',
            'qc_id',
            'nian',
            'qi',
            'sblx',
            'sbqy',
            'fylx',
            'sbtj',
            'czr',
            'sex',
            'czrzjhm',
            'czrlxdh',
            'czrgzdw',
            'sqr_info',
            'family_info',
            'other_info',
            'sb_date',
            'xq_id',
            'sbxq',
            'sbhx',
            'bzfs',
            // 'status',
            // 'add_user',
            'sl_date',
            // 'hzbh',
            'bz',
            'zm_files'
        ]);

        $data['status'] = ApplyStatusEnum::TEMP->value;
        $data['sbtj'] = ApplyModel::SBTJ['OFFLINE'][0];
        $data['add_user'] = $this->auth->username;

        try {
            validate(FamilyMemberValidate::class)->scene('PreCheck')->check($data['sqr_info']);
        } catch (ValidateException $e) {
            return $this->error('申请人信息：' . $e->getMessage());
        }

        foreach ($data['family_info'] as $item) {
            try {
                validate(FamilyMemberValidate::class)->scene('PreCheck')->check($item);
            } catch (ValidateException $e) {
                return $this->error('家庭成员信息：' . ($item['name'] ?? '') . ' ' . $e->getMessage());
            }
        }

        try {
            validate(ApplyValidate::class)->scene('stage')->check($data);
            $this->logic->stage($data);
            return $this->success('暂存成功');
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }
    }
}
