<?php

namespace app\controller\backend\http\ration;

use app\common\controller\Backend;
use app\controller\backend\logic\ration\ContractRenewApplyLogic;
use app\controller\backend\logic\ration\LeaseLogic;
use app\enums\ration\LeaseStatusEnum;
use app\service\TenantReviewService;
use think\App;
use think\db\exception\DbException;
use think\response\Json;

class Review extends Backend
{
    protected LeaseLogic $logic;

    public function __construct(APP $app, LeaseLogic $logic)
    {
        parent::__construct($app);

        $this->logic = $logic;
        $this->logic->setUid($this->auth->id);
    }

    /**
     * @throws DbException
     */
    public function index(): Json
    {
        $where = [];
        $limit = $this->request->param('limit/d', 15);
        $params = $this->request->param();

        $where[] = ['status', 'in', [LeaseStatusEnum::PENDING_REVIEW->value, LeaseStatusEnum::REVIEW->value, LeaseStatusEnum::RENEWED->value]];
        if (($params['status'] ?? null) !== '') {
            $where[] = ['status', '=', $params['status']];
        }

        $data = $this->logic->setBeforeGetList(function ($query) {
            return $query->with(['archive', 'renew']);
        })->getList($where, '*', 'id desc', $limit);

        return $this->success('success', $data);
    }

    public function addReview(): Json
    {
        $params = $this->request->param();

        $params['add_user'] = $this->auth->username;
        $params['admin_id'] = $this->auth->id;

        try {
            $result = (new TenantReviewService())->handleTenantReview($params);
            return $this->success('年审申请已提交', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    public function finishedReview(): Json
    {
        $id = $this->request->param('id/d', 0);
        if (!$id) {
            return $this->error('参数错误');
        }
        (new ContractRenewApplyLogic())->finished(['id' => $id]);

        return $this->success('操作成功');
    }


}
