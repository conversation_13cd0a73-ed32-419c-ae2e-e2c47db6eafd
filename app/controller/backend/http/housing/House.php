<?php

namespace app\controller\backend\http\housing;

use app\common\controller\Backend;
use app\controller\backend\logic\HouseLogic;
use app\validate\HouseValidate;
use think\App;
use think\exception\ValidateException;
use think\response\Json;
use app\common\facade\Dict;

class House extends Backend
{
    protected HouseLogic $logic;

    public function __construct(APP $app, HouseLogic $logic)
    {
        parent::__construct($app);

        $this->logic = $logic;
        $this->logic->setUid($this->auth->id);
    }

    public function index(): Json
    {
        $fields = 'House.*';
        $where = [];
        $limit = $this->request->param('limit/d', 15);
        $params = $this->request->param();
        isset($params['xqmc']) && $params['xqmc'] && $where[] = ['House.xqmc', 'like', "%{$params['xqmc']}%"];
        isset($params['xq_id']) && $params['xq_id'] && $where[] = ['House.xq_id', '=', $params['xq_id']];
        isset($params['ld_id']) && $params['ld_id'] && $where[] = ['House.ld_id', '=', $params['ld_id']];
        isset($params['ld_id']) && $params['ld_id'] && $where[] = ['House.ld_id', '=', $params['ld_id']];
        isset($params['ldmc']) && $params['ldmc'] && $where[] = ['House.ldmc', 'like', "%{$params['ldmc']}%"];
        isset($params['dy']) && $params['dy'] != '' && $where[] = ['House.dy', 'like', "%{$params['dy']}%"];
        isset($params['lc']) && $params['lc'] != '' && $where[] = ['House.lc', '=', $params['lc']];
        isset($params['sh']) && $params['sh'] != '' && $where[] = ['House.sh', 'like', "%{$params['sh']}%"];
        isset($params['hx']) && $params['hx'] && $where[] = ['House.hx', '=', $params['hx']];
        isset($params['fwxz']) && $params['fwxz'] != '' && $where[] = ['House.fwxz', '=', $params['fwxz']];
        isset($params['fwzt']) && $params['fwzt'] != '' && $where[] = ['House.fwzt', 'in', $params['fwzt']];
        isset($params['wxzt']) && $params['wxzt'] != '' && $where[] = ['House.wxzt', 'in', $params['wxzt']];
        isset($params['jzmj']) && $params['jzmj'] != '' && $where[] = ['House.jzmj', 'between', [$params['jzmj'][0], $params['jzmj'][1]]];

        if (isset($params['area_code']) && $params['area_code']) {
            $where[] = ['House.area_code', 'in', $this->logic->getSubAreas($params['area_code'])];
        }

        $optionsMore = [];
        $optionsMore['options_with'] = ['lock'];

        $closureWhere = [];
        if (isset($params['has_lock'])) {
            if ($params['has_lock'] == '0') { // 未绑锁
                $closureWhere[] = function ($query) {
                    $query->where('House.lock_id', 'null');
                };
            } elseif ($params['has_lock'] == '1') { // 已绑锁
                $closureWhere[] = function ($query) {
                    $query->where('House.lock_id', 'not null');
                };
            }
        }
        !empty($closureWhere) && $optionsMore['options_closureWhere'] = $closureWhere;

        // 门锁筛选条件
        $lockWhere = [];
        isset($params['brand_id']) && $params['brand_id'] != '' && $lockWhere[] = ['Lock.brand_id', '=', $params['brand_id']];
        isset($params['device_sn']) && $params['device_sn'] != '' && $lockWhere[] = ['Lock.device_sn', 'like', "%{$params['device_sn']}%"];
        $optionsMore['options_haswhere'] = [
            'lock' => function ($query) use ($lockWhere) {
                $query->where($lockWhere);
            }
        ];

        $data = $this->logic->getList($where, $fields, '', $limit, $optionsMore);

        return $this->success('success', $data);
    }

    public function add(): Json
    {
        $data = $this->request->only([
            'xq_id',
            'ld_id',
            'xqmc',
            'ldmc',
            'dy',
            'lc',
            'xh',
            'sh',
            'hx',
            'jzmj',
            'fwxz',
            'fwzt',
            'bz',
            'area_code'
        ]);

        try {
            validate(HouseValidate::class)->scene('add')->check($data);
            $this->logic->add($data);
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }

        return $this->success('添加成功');
    }

    public function detail(): Json
    {
        $data = $this->request->only([
            'id'
        ]);
        $data = $this->logic->detail($data['id']);

        return $this->success('success', $data);
    }

    public function edit(): Json
    {
        $data = $this->request->only([
            'id',
            'xq_id',
            'ld_id',
            'xqmc',
            'ldmc',
            'dy',
            'lc',
            'xh',
            'sh',
            'hx',
            'jzmj',
            'fwxz',
            'fwzt',
            'bz',
            'area_code'
        ]);
        if (!$data) {
            return $this->error('参数不能为空');
        }

        try {
            validate(HouseValidate::class)->scene('edit')->check($data);
            $this->logic->edit($data);
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }

        return $this->success('更新成功');
    }

    public function del(): Json
    {
        $data = $this->request->only([
            'id'
        ]);

        try {
            validate(HouseValidate::class)->scene('del')->check($data);
            $this->logic->del($data);
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }

        return $this->success('删除成功');
    }

    public function batchDel(): Json
    {
        $data = $this->request->only([
            'ids'
        ]);

        try {
            validate(HouseValidate::class)->scene('batchDel')->check($data);
            $this->logic->del($data['ids']);
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        }

        return $this->success('删除成功');
    }

    public function import(): Json
    {
        $data = $this->request->only([
            'import_info' => [],
        ]);

        try {
            if (empty($data['import_info']) || !is_array($data['import_info'])) {
                return $this->error('导入数据不能为空');
            }

            // 使用优化后的导入方法
            $result = $this->optimizedImport($data['import_info']);

            if ($result['success']) {
                return $this->success('导入成功');
            } else {
                return $this->error($result['message']);
            }
        } catch (ValidateException $e) {
            return $this->error($e->getMessage());
        } catch (\Exception $e) {
            return $this->error('导入失败：' . $e->getMessage());
        }
    }

    /**
     * 优化后的房源导入方法
     * @param array $importData
     * @return array
     */
    private function optimizedImport(array $importData): array
    {
        $t_row_key = 0;

        try {
            // 1. 预加载所有字典数据
            $dictMaps = $this->preloadDictData();

            // 2. 收集所有需要查询的小区和楼栋信息
            $estateKeys = [];
            $buildingKeys = [];

            foreach ($importData as $item) {
                $t_row_key++;

                // 基础字段验证
                $this->validateBasicFields($item, $t_row_key);

                // 收集小区查询键
                $areaCode = $dictMaps['areas_code'][$item['area_code']] ?? 0;
                $estateKey = $areaCode . '|' . $item['xqmc'];
                $estateKeys[$estateKey] = ['area_code' => $areaCode, 'xqmc' => $item['xqmc']];

                // 收集楼栋查询键（暂时用小区名称，后面会替换为小区ID）
                $buildingKey = $item['xqmc'] . '|' . $item['ldmc'];
                $buildingKeys[$buildingKey] = ['xqmc' => $item['xqmc'], 'ldmc' => $item['ldmc']];
            }

            // 3. 批量查询小区数据
            $estates = $this->batchQueryEstates($estateKeys);

            // 4. 批量查询楼栋数据
            $buildings = $this->batchQueryBuildings($buildingKeys, $estates);

            // 5. 处理数据映射和验证
            $processedData = [];
            $t_row_key = 0;

            foreach ($importData as $item) {
                $t_row_key++;

                // 字典映射
                $item = $this->mapDictValues($item, $dictMaps);

                // 小区映射
                $estateKey = $item['area_code'] . '|' . $item['xqmc'];
                if (!isset($estates[$estateKey])) {
                    return ['success' => false, 'message' => "第{$t_row_key}条数据有误：小区不存在"];
                }
                $item['xq_id'] = $estates[$estateKey]['id'];

                // 楼栋映射
                $buildingKey = $item['xq_id'] . '|' . $item['ldmc'];
                if (!isset($buildings[$buildingKey])) {
                    return ['success' => false, 'message' => "第{$t_row_key}条数据有误：楼栋不存在"];
                }
                $item['ld_id'] = $buildings[$buildingKey]['id'];

                // 最终验证
                validate(HouseValidate::class)->scene('add')->check($item);

                $processedData[] = $item;
            }

            // 6. 批量导入
            $this->logic->batchImport($processedData);

            return ['success' => true, 'message' => '导入成功'];

        } catch (ValidateException $e) {
            return ['success' => false, 'message' => "第{$t_row_key}条数据有误：" . $e->getMessage()];
        }
    }

    /**
     * 预加载所有字典数据
     * @return array
     */
    private function preloadDictData(): array
    {
        return [
            'areas_code' => $this->buildDictMap(Dict::getDictData('areas_code')),
            'fwxz' => $this->buildDictMap(Dict::getDictData('fwxz')),
            'fwzt' => $this->buildDictMap(Dict::getDictData('fwzt')),
            'huxing' => $this->buildDictMap(Dict::getDictData('huxing')),
        ];
    }

    /**
     * 构建字典映射表
     * @param array $dictData
     * @return array
     */
    private function buildDictMap(array $dictData): array
    {
        $map = [];
        foreach ($dictData as $item) {
            $map[$item['label']] = $item['value'];
        }
        return $map;
    }

    /**
     * 验证基础字段
     * @param array $item
     * @param int $rowKey
     * @throws ValidateException
     */
    private function validateBasicFields(array $item, int $rowKey): void
    {
        validate([
            'area_code' => 'require',
            'fwxz' => 'require',
            'fwzt' => 'require',
            'hx' => 'require',
            'xqmc' => 'require',
            'ldmc' => 'require',
        ])->check($item);
    }

    /**
     * 批量查询小区数据
     * @param array $estateKeys
     * @return array
     */
    private function batchQueryEstates(array $estateKeys): array
    {
        if (empty($estateKeys)) {
            return [];
        }

        $estates = [];

        // 提取所有唯一的小区名称和区域代码
        $xqmcList = [];
        $areaCodeList = [];
        foreach ($estateKeys as $info) {
            $xqmcList[] = $info['xqmc'];
            $areaCodeList[] = $info['area_code'];
        }
        $xqmcList = array_unique($xqmcList);
        $areaCodeList = array_unique($areaCodeList);

        // 批量查询
        $estateList = \app\model\Estate::whereIn('xqmc', $xqmcList)
            ->whereIn('area_code', $areaCodeList)
            ->field('id,area_code,xqmc')
            ->select();

        // 构建映射表
        foreach ($estateList as $estate) {
            $key = $estate['area_code'] . '|' . $estate['xqmc'];
            $estates[$key] = $estate;
        }

        return $estates;
    }

    /**
     * 批量查询楼栋数据
     * @param array $buildingKeys
     * @param array $estates
     * @return array
     */
    private function batchQueryBuildings(array $buildingKeys, array $estates): array
    {
        if (empty($buildingKeys) || empty($estates)) {
            return [];
        }

        $buildings = [];

        // 收集所有需要查询的小区ID和楼栋名称
        $xqIds = [];
        $ldmcList = [];

        foreach ($buildingKeys as $info) {
            foreach ($estates as $estateKey => $estate) {
                if (strpos($estateKey, '|' . $info['xqmc']) !== false) {
                    $xqIds[] = $estate['id'];
                    $ldmcList[] = $info['ldmc'];
                    break;
                }
            }
        }

        if (empty($xqIds)) {
            return [];
        }

        $xqIds = array_unique($xqIds);
        $ldmcList = array_unique($ldmcList);

        // 批量查询
        $buildingList = \app\model\Building::whereIn('xq_id', $xqIds)
            ->whereIn('ldmc', $ldmcList)
            ->field('id,xq_id,ldmc')
            ->select();

        // 构建映射表
        foreach ($buildingList as $building) {
            $key = $building['xq_id'] . '|' . $building['ldmc'];
            $buildings[$key] = $building;
        }

        return $buildings;
    }

    /**
     * 映射字典值
     * @param array $item
     * @param array $dictMaps
     * @return array
     */
    private function mapDictValues(array $item, array $dictMaps): array
    {
        // 行政区划映射
        $item['area_code'] = $dictMaps['areas_code'][$item['area_code']] ?? 0;
        // 房屋性质映射
        $item['fwxz'] = $dictMaps['fwxz'][$item['fwxz']] ?? 0;
        // 房屋状态映射
        $item['fwzt'] = $dictMaps['fwzt'][$item['fwzt']] ?? 0;
        // 户型映射
        $item['hx'] = $dictMaps['huxing'][$item['hx']] ?? 0;

        return $item;
    }
}
