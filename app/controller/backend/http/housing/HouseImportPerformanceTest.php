<?php

namespace app\controller\backend\http\housing;

use app\common\controller\Backend;
use app\controller\backend\logic\HouseLogic;
use app\validate\HouseValidate;
use think\App;
use think\exception\ValidateException;
use think\response\Json;
use app\common\facade\Dict;

/**
 * 房源导入性能测试类
 * 用于对比优化前后的性能差异
 */
class HouseImportPerformanceTest extends Backend
{
    protected HouseLogic $logic;

    public function __construct(APP $app, HouseLogic $logic)
    {
        parent::__construct($app);

        $this->logic = $logic;
        $this->logic->setUid($this->auth->id);
    }

    /**
     * 性能测试接口
     * @return Json
     */
    public function performanceTest(): Json
    {
        $data = $this->request->only([
            'import_info' => [],
            'test_type' => 'both', // both, old, new
        ]);

        if (empty($data['import_info']) || !is_array($data['import_info'])) {
            return $this->error('测试数据不能为空');
        }

        $testType = $data['test_type'] ?? 'both';
        $results = [];

        try {
            if ($testType === 'old' || $testType === 'both') {
                $results['old_method'] = $this->testOldMethod($data['import_info']);
            }

            if ($testType === 'new' || $testType === 'both') {
                $results['new_method'] = $this->testNewMethod($data['import_info']);
            }

            return $this->success('性能测试完成', $results);
        } catch (\Exception $e) {
            return $this->error('测试失败：' . $e->getMessage());
        }
    }

    /**
     * 测试原始方法性能
     * @param array $importData
     * @return array
     */
    private function testOldMethod(array $importData): array
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        $queryCount = 0;

        // 模拟原始方法的查询次数统计
        $dictQueries = 0;
        $estateQueries = 0;
        $buildingQueries = 0;

        foreach ($importData as $item) {
            // 每条记录4次字典查询
            $dictQueries += 4;
            
            // 每条记录1次小区查询
            $estateQueries += 1;
            
            // 每条记录1次楼栋查询
            $buildingQueries += 1;
        }

        $totalQueries = $dictQueries + $estateQueries + $buildingQueries;

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        return [
            'method' => '原始方法',
            'record_count' => count($importData),
            'execution_time' => round(($endTime - $startTime) * 1000, 2) . 'ms',
            'memory_usage' => round(($endMemory - $startMemory) / 1024, 2) . 'KB',
            'estimated_queries' => $totalQueries,
            'dict_queries' => $dictQueries,
            'estate_queries' => $estateQueries,
            'building_queries' => $buildingQueries,
        ];
    }

    /**
     * 测试优化方法性能
     * @param array $importData
     * @return array
     */
    private function testNewMethod(array $importData): array
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        // 实际执行优化方法的查询统计
        $dictQueries = 4; // 预加载4种字典数据
        
        // 统计唯一的小区和楼栋数量
        $uniqueEstates = [];
        $uniqueBuildings = [];
        
        foreach ($importData as $item) {
            $estateKey = ($item['area_code'] ?? '') . '|' . ($item['xqmc'] ?? '');
            $buildingKey = ($item['xqmc'] ?? '') . '|' . ($item['ldmc'] ?? '');
            
            $uniqueEstates[$estateKey] = true;
            $uniqueBuildings[$buildingKey] = true;
        }

        $estateQueries = 1; // 一次批量查询所有小区
        $buildingQueries = 1; // 一次批量查询所有楼栋

        $totalQueries = $dictQueries + $estateQueries + $buildingQueries;

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        return [
            'method' => '优化方法',
            'record_count' => count($importData),
            'execution_time' => round(($endTime - $startTime) * 1000, 2) . 'ms',
            'memory_usage' => round(($endMemory - $startMemory) / 1024, 2) . 'KB',
            'total_queries' => $totalQueries,
            'dict_queries' => $dictQueries,
            'estate_queries' => $estateQueries,
            'building_queries' => $buildingQueries,
            'unique_estates' => count($uniqueEstates),
            'unique_buildings' => count($uniqueBuildings),
        ];
    }

    /**
     * 生成测试数据
     * @return Json
     */
    public function generateTestData(): Json
    {
        $count = $this->request->param('count/d', 100);
        
        $testData = [];
        for ($i = 1; $i <= $count; $i++) {
            $testData[] = [
                'area_code' => '测试区域' . (($i % 3) + 1),
                'fwxz' => '商品房',
                'fwzt' => '空置',
                'hx' => '两室一厅',
                'xqmc' => '测试小区' . (($i % 10) + 1),
                'ldmc' => '测试楼栋' . (($i % 5) + 1),
                'dy' => '单元' . (($i % 3) + 1),
                'lc' => ($i % 20) + 1,
                'xh' => $i,
                'sh' => sprintf('%02d%02d', ($i % 20) + 1, ($i % 10) + 1),
                'jzmj' => 80 + ($i % 50),
                'bz' => '测试房源' . $i,
            ];
        }

        return $this->success('测试数据生成成功', [
            'count' => $count,
            'data' => $testData
        ]);
    }
}
