<?php
declare(strict_types=1);

namespace app\command;

use app\controller\backend\model\ration\Contract;
use app\controller\backend\model\ration\ContractRenewApply;
use app\controller\backend\model\ration\Lease;
use app\enums\ration\ContractStatusEnum;
use app\enums\ration\LeaseStatusEnum;
use think\console\Input;
use think\console\Output;
use think\console\Command;
use think\db\exception\DbException;
use think\facade\Db;

class RunTenantsReview extends Command
{
    protected function configure(): void
    {
        // 指令配置
        $this->setName('TenantsReview')
            ->setDescription('执行租户年度审核')
            ->addArgument('action', null, '操作类型', 'review');
    }

    protected function execute(Input $input, Output $output): void
    {
        $action = $input->getArgument('action');
        switch ($action) {
            case 'review':
                $this->runReview($output);
                break;
            case 'completed':
                $this->runCompleted($output);
                break;
            default:
                $output->writeln('未知操作类型: ' . $action);
        }
    }

    /**
     * 处理租户年度审核检查
     * @param Output $output
     */
    private function runReview(Output $output): void
    {
        try {
            $i = 0;

            $contractQuery = Contract::where('status', ContractStatusEnum::IN_PROGRESS->value)
                ->whereBetween('ht_jsrq', [
                    date('Y-m-d'),
                    date('Y-m-d', strtotime('+30 days'))
                ]);

            Lease::withoutGlobalScope()
                ->hasWhere('contract', $contractQuery)
                ->where('status', '=', LeaseStatusEnum::CHECKED_IN->value)
                ->chunk(100, function ($items) use ($output, &$i) {
                    foreach ($items as $item) {
                        $i++;
                        $item->status = LeaseStatusEnum::PENDING_REVIEW->value;
                        $item->save();
                        $output->writeln($i . ': 租户(' . $item->id . '): ' . $item->contract->czr . ' ' . $item->contract->ht_ksrq . ' ' . $item->contract->ht_jsrq . ' 配租状态已更新为: ' . LeaseStatusEnum::PENDING_REVIEW->label());
                    }
                });

            $output->writeln('tenants review success' . ' Total: ' . $i . ' records processed.');
        } catch (\Exception $e) {
            $output->writeln('tenants review fail' . $e->getMessage());
        }
    }

    /**
     * 处理已完成的租户审核逻辑
     * @param Output $output
     * @throws DbException
     */
    private function runCompleted(Output $output): void
    {
        ContractRenewApply::where('status', 1)
            ->whereDay('ht_ksrq', '2025-07-01')
            ->chunk(100, function ($items) use ($output) {
                foreach ($items as $item) {
                    $this->processSingleTenantReview($item, $output);
                }
            });
    }

    /**
     * 租户年审处理
     */
    private function processSingleTenantReview($item, Output $output): void
    {
        $oldLease = $item->lease;
        $newLease = $item->newLease;
        $oldContract = $item->contract;
        $newContract = $item->newContract;

        // 前置校验
        if ($newLease->isEmpty() || $newContract->isEmpty() ||
            $newLease->status !== LeaseStatusEnum::SIGNED->value ||
            $newContract->status !== ContractStatusEnum::SIGNED->value
        ) {
            $output->writeln("租户信息异常: LeaseStatus={$oldLease->status}, ContractStatus={$newContract->status}");
            return;
        }

        Db::transaction(function () use ($oldLease, $newLease, $oldContract, $newContract, $output) {
            // 旧记录失效
            $oldLease->status = LeaseStatusEnum::INVALIDATED->value;
            $oldContract->status = ContractStatusEnum::RENEWED->value;

            // 新记录生效
            $newContract->status = ContractStatusEnum::IN_PROGRESS->value;
            $newLease->status = LeaseStatusEnum::CHECKED_IN->value;

            $oldLease->save();
            $oldContract->save();
            $newContract->save();
            $newLease->save();

            $output->writeln("租户({$newLease->archive_id}) 年审事务提交成功");
        });
    }

}
