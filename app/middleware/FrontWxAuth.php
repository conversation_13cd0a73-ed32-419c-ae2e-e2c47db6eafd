<?php
declare(strict_types=1);

namespace app\middleware;

use app\controller\front\library\wx\Auth;
use Exception;
use think\Response;
use Throwable;
use think\facade\Cookie;

class FrontWxAuth
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle($request, \Closure $next)
    {
        try {
            $auth = Auth::instance();
            $token = $request->server(
                'HTTP_AUTHORIZATION',
                $request->request('token', Cookie::get('token') ?: $request->server('HTTP_TOKEN'))
            );

            if (strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
            }

            $auth->init($token);
            if (!$auth->isLogin()) {
                throw new Exception($auth->getError());
            }

            $request->auth_member = $auth;

        } catch (Throwable $e) {
            return json(['code' => 409, 'msg' => '请先登录', 'time' => $request->server('REQUEST_TIME'), 'data' => []]);
        }

        return $next($request);
    }
}
