<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use app\middleware\BackendAuth;
use app\middleware\BackendPermission;
use think\facade\Route;

Route::group('backend', function () {
    Route::post('login', 'backend.http.index/login');
    Route::post('sms-login', 'backend.http.index/smsLogin');
    Route::rule('logout', 'backend.http.index/logout');
    Route::post('refresh-token', 'backend.http.index/refreshToken');
    Route::get('captcha', 'backend.http.index/captcha');
    // Route::post('register', 'backend.http.index/register');
    Route::post('code', 'backend.http.index/getCode');
    Route::post('forgot', 'backend.http.index/forgotPwd');
    Route::post('index', 'backend.http.Test/index');

    Route::group('', function () {
        Route::get('get-async-routes', 'backend.http.index/getRoutes');
        Route::get('all-dict-data', 'backend.http.system.dict/getDictData');
        Route::get('build-suffix-svg', 'backend.http.ajax/buildSuffixSvg');
        Route::post('upload', 'backend.http.ajax/upload');

        Route::group('profile', function () {
            Route::get('mine', 'backend.http.profile/index');
            Route::post('edit', 'backend.http.profile/edit');
            Route::post('update-pwd', 'backend.http.profile/updatePwd');
        });

        // TODO
        // workflow 流程节点状态
        Route::post('node-state', 'backend.http.workflow.processInstance/nodeState');
        Route::get('role/:type', 'backend.http.system.Role/getUserByRole');
    })->middleware([
        BackendAuth::class,
    ]);

    Route::group('', function () {
        Route::group('system', function () {
            Route::group('area', function () {
                Route::get('list', 'backend.http.system.area/index');
            });
            Route::group('street-code', function () {
                Route::get('list', 'backend.http.system.StreetCode/index');
                Route::post('', 'backend.http.system.StreetCode/add');
                Route::get(':id', 'backend.http.system.StreetCode/detail');
                Route::put(':id', 'backend.http.system.StreetCode/edit');
                Route::delete(':id', 'backend.http.system.StreetCode/del');
                Route::post('batchDel', 'backend.http.system.StreetCode/batchDel');
            });
            Route::group('menu', function () {
                Route::get('list', 'backend.http.system.menu/index');
                Route::post('', 'backend.http.system.menu/add');
                Route::get(':id', 'backend.http.system.menu/detail');
                Route::put(':id', 'backend.http.system.menu/edit');
                Route::delete(':id', 'backend.http.system.menu/del');
            });
            Route::group('role', function () {
                Route::get('list', 'backend.http.system.role/index');
                Route::post('', 'backend.http.system.role/add');
                Route::get(':id', 'backend.http.system.role/detail');
                Route::put(':id', 'backend.http.system.role/edit');
                Route::delete(':id', 'backend.http.system.role/del');
                Route::post('status', 'backend.http.system.role/status');
            });
            Route::group('dept', function () {
                Route::get('list', 'backend.http.system.dept/index');
                Route::post('', 'backend.http.system.dept/add');
                Route::get(':id', 'backend.http.system.dept/detail');
                Route::put(':id', 'backend.http.system.dept/edit');
                Route::delete(':id', 'backend.http.system.dept/del');
            });
            Route::group('user', function () {
                Route::get('list', 'backend.http.system.user/index');
                Route::post('', 'backend.http.system.user/add');
                Route::get(':id', 'backend.http.system.user/detail');
                Route::put(':id', 'backend.http.system.user/edit');
                Route::delete(':id', 'backend.http.system.user/del');
                Route::post('batchDel', 'backend.http.system.user/batchDel');
                Route::post('setRoles', 'backend.http.system.user/setUserRoles');
                Route::post('restPwd', 'backend.http.system.user/restPwd');
                Route::post('status', 'backend.http.system.user/status');
            });
            Route::group('dict', function () {
                Route::get('list', 'backend.http.system.dict/index');
                Route::post('', 'backend.http.system.dict/add');
                Route::put(':id', 'backend.http.system.dict/edit');
                Route::delete(':id', 'backend.http.system.dict/del');
                Route::post('batchDel', 'backend.http.system.dict/batchDel');
            });
            Route::group('dict-data', function () {
                Route::get('list', 'backend.http.system.dictData/index');
                Route::post('', 'backend.http.system.dictData/add');
                Route::get(':id', 'backend.http.system.dictData/detail');
                Route::put(':id', 'backend.http.system.dictData/edit');
                Route::delete(':id', 'backend.http.system.dictData/del');
                Route::post('batchDel', 'backend.http.system.dictData/batchDel');
            });
            Route::group('config', function () {
                Route::get('list', 'backend.http.system.config/index');
                Route::post('', 'backend.http.system.config/add');
                Route::get('configs', 'backend.http.system.config/configs');
                Route::post('update', 'backend.http.system.config/update');
                Route::put(':id', 'backend.http.system.config/edit');
                Route::delete(':id', 'backend.http.system.config/del');
            });
            Route::group('attachment', function () {
                Route::get('list', 'backend.http.system.attachment/index');
                Route::delete(':id', 'backend.http.system.attachment/del');
                Route::post('batchDel', 'backend.http.system.attachment/batchDel');
            });
            Route::group('print-template', function () {
                Route::get('list', 'backend.http.system.printTemplate/index');
                Route::post('', 'backend.http.system.printTemplate/add');
                Route::put(':id', 'backend.http.system.printTemplate/edit');
                Route::delete(':id', 'backend.http.system.printTemplate/del');
                Route::get(':id', 'backend.http.system.printTemplate/detail');
                Route::post('batchDel', 'backend.http.system.printTemplate/batchDel');
                Route::post('status', 'backend.http.system.printTemplate/status');
            });
            Route::group('log', function () {
                Route::get('list', 'backend.http.system.logs/index');
                Route::get(':id', 'backend.http.system.logs/detail');
            });

            Route::group('management-area', function () {
                Route::get('list', 'backend.http.system.ManagementArea/index');
                Route::get(':id', 'backend.http.system.ManagementArea/detail');

                Route::get('mine', 'backend.http.system.ManagementArea/mine');
                Route::put('set-current/:id', 'backend.http.system.ManagementArea/setCurrent');
            });

            Route::group('banner', function () {
                Route::get('list', 'backend.http.system.Banner/index');
                Route::post('', 'backend.http.system.Banner/add');
                Route::get(':id', 'backend.http.system.Banner/detail');
                Route::put(':id', 'backend.http.system.Banner/edit');
                Route::delete(':id', 'backend.http.system.Banner/del');
                Route::post('batchDel', 'backend.http.system.Banner/batchDel');
                Route::post('status', 'backend.http.system.Banner/status');
            });

            // 通知公告
            Route::group('notice', function () {
                Route::get('list', 'backend.http.system.Notice/index');
                Route::post('', 'backend.http.system.Notice/add');
                Route::get(':id', 'backend.http.system.Notice/detail');
                Route::put(':id', 'backend.http.system.Notice/edit');
                Route::delete(':id', 'backend.http.system.Notice/del');
                Route::post('batchDel', 'backend.http.system.Notice/batchDel');
                Route::post('status', 'backend.http.system.Notice/status');
            });
        });

        Route::group('housing', function () {
            Route::group('estate', function () {
                Route::get('list', 'backend.http.housing.estate/index');
                Route::post('', 'backend.http.housing.estate/add');
                Route::get(':id', 'backend.http.housing.estate/detail');
                Route::put(':id', 'backend.http.housing.estate/edit');
                Route::delete(':id', 'backend.http.housing.estate/del');
                Route::post('import', 'backend.http.housing.estate/import');
            });
            Route::group('building', function () {
                Route::get('list', 'backend.http.housing.building/index');
                Route::post('', 'backend.http.housing.building/add');
                Route::get(':id', 'backend.http.housing.building/detail');
                Route::put(':id', 'backend.http.housing.building/edit');
                Route::delete(':id', 'backend.http.housing.building/del');
                Route::post('import', 'backend.http.housing.building/import');
            });
            Route::group('house', function () {
                Route::get('list', 'backend.http.housing.house/index');
                Route::post('', 'backend.http.housing.house/add');
                Route::get(':id', 'backend.http.housing.house/detail');
                Route::put(':id', 'backend.http.housing.house/edit');
                Route::delete(':id', 'backend.http.housing.house/del');
                Route::post('batchDel', 'backend.http.housing.house/batchDel');
                Route::post('import', 'backend.http.housing.house/import');
            });

            // 户型图
            Route::group('apartment-layout', function () {
                Route::get('list', 'backend.http.housing.ApartmentLayout/index');
                Route::post('', 'backend.http.housing.ApartmentLayout/add');
                Route::get(':id', 'backend.http.housing.ApartmentLayout/detail');
                Route::put(':id', 'backend.http.housing.ApartmentLayout/edit');
                Route::delete(':id', 'backend.http.housing.ApartmentLayout/del');
                Route::post('status', 'backend.http.housing.ApartmentLayout/status');
            });
        });

        Route::group('ration', function () {
            // 配前-期次
            Route::group('period', function () {
                Route::get('list', 'backend.http.ration.Period/index');
                Route::post('', 'backend.http.ration.Period/add');
                Route::get(':id', 'backend.http.ration.Period/detail');
                Route::put(':id', 'backend.http.ration.Period/edit');
                Route::delete(':id', 'backend.http.ration.Period/del');
                Route::post('status', 'backend.http.ration.Period/status');
                Route::get('house-list', 'backend.http.ration.Period/houseList');
                Route::get('get-one', 'backend.http.ration.Period/getOne');
                Route::post('update-config/:id', 'backend.http.ration.Period/updateConfig');
                Route::post('confirm-config/:id', 'backend.http.ration.Period/confirmConfig');
            });

            // 配前-申请
            Route::group('apply', function () {
                Route::get('list', 'backend.http.ration.Apply/index');
                Route::post('', 'backend.http.ration.Apply/add');
                Route::get(':id', 'backend.http.ration.Apply/detail');
                Route::put(':id', 'backend.http.ration.Apply/edit');
                Route::delete(':id', 'backend.http.ration.Apply/del');

                Route::post('check-apply', 'backend.http.ration.Apply/checkApply'); // 检查申请
                Route::post('stage', 'backend.http.ration.Apply/stage'); // 暂存
            });

            // 资格核验
            Route::group('eligible', function () {
                Route::get('list', 'backend.http.ration.Eligible/index');
                Route::get(':id', 'backend.http.ration.Eligible/detail');
                Route::get('get-new-zgbh', 'backend.http.ration.Eligible/getNewZgbh'); // 获取可用资格证号
                Route::post('set-zgbh', 'backend.http.ration.Eligible/setZgbh'); // 设置资格证号
                Route::get('get-new-zgbh-batch', 'backend.http.ration.Eligible/getNewZgbhBatch'); // 批量获取可用资格证号
                Route::post('set-zgbh-batch', 'backend.http.ration.Eligible/setZgbhBatch'); // 批量设置资格证号
                Route::post('gs', 'backend.http.ration.Eligible/gongshi'); // 提交公示
                Route::post('cancel-gs', 'backend.http.ration.Eligible/cancelGongshi'); // 撤销公示
                Route::get('export-qualification', 'backend.http.ration.Eligible/exportQualification');// 导出资格列表
            });

            // 配租管理-待配租的
            Route::group('lease', function () {
                Route::get('list', 'backend.http.ration.Lease/index');
                Route::get(':id', 'backend.http.ration.Lease/detail');
                Route::get('record', 'backend.http.ration.Lease/record');

                Route::get('current/:id', 'backend.http.ration.Lease/current');

                Route::post('dr-yaohao', 'backend.http.ration.Lease/drYaohao');

                Route::post('ruzhu', 'backend.http.ration.Lease/ruzhu');
            });

            // 轮候管理
            Route::group('waiting', function () {
                Route::get('list', 'backend.http.ration.Waiting/index');
            });

            // 合同管理
            Route::group('contract', function () {
                Route::get('list', 'backend.http.ration.Contract/index');
                Route::get(':id', 'backend.http.ration.Contract/detail');
                Route::put(':id', 'backend.http.ration.Contract/edit');
                // Route::delete(':id', 'backend.http.ration.Contract/del');

                Route::post('sign-ht', 'backend.http.ration.Contract/signHt');
            });

            // 租户管理 当前有效的租户
            Route::group('tenant', function () {
                Route::get('list', 'backend.http.ration.Tenant/index');
                Route::post('before-checkout', 'backend.http.ration.Tenant/beforeCheckout');
                Route::post('checkout', 'backend.http.ration.Tenant/checkout');
                Route::post('checkout-completed', 'backend.http.ration.Tenant/checkoutCompleted');
            });

            // 年审管理
            Route::group('review', function () {
                Route::get('list', 'backend.http.ration.Review/index');
                Route::post('add-review', 'backend.http.ration.Review/addReview');
                Route::post('finished', 'backend.http.ration.Review/finishedReview');
            });

            // 档案管理 已归档的
            Route::group('archive', function () {
                Route::get('list', 'backend.http.ration.Archive/index');
                Route::get(':id', 'backend.http.ration.Archive/detail');
                // Route::put(':id', 'backend.http.ration.Archive/edit');
                // Route::delete(':id', 'backend.http.ration.Archive/del');

                Route::get('family/:id', 'backend.http.ration.Archive/family');


            });

            // 配租-合同续签-申请
            Route::group('contract-renew-apply', function () {
                Route::get('list', 'backend.http.ration.ContractRenewApply/index');
                Route::post('apply', 'backend.http.ration.ContractRenewApply/apply');
                Route::post('sp', 'backend.http.ration.ContractRenewApply/sp'); // 审批
            });

            // 配租-账单
            Route::group('bill', function () {
                Route::get('list', 'backend.http.ration.Bill/index');
                Route::get('record', 'backend.http.ration.Bill/record');
                Route::post('confirm', 'backend.http.ration.Bill/confirm'); // 确认收款
                Route::post('xj-zujin', 'backend.http.ration.Bill/xjZujin'); // 新增续交租金账单
            });
            // 配租-账单-账单项
            Route::group('bill-item', function () {
                Route::get('list', 'backend.http.ration.BillItem/index');
            });

            Route::group('lease-log', function () {
                Route::get('list', 'backend.http.ration.LeaseLog/index');
            });

            Route::group('price', function () {
                Route::get('list', 'backend.http.ration.Price/index');
                Route::post('', 'backend.http.ration.Price/add');
                Route::get(':id', 'backend.http.ration.Price/detail');
                Route::put(':id', 'backend.http.ration.Price/edit');
                Route::delete(':id', 'backend.http.ration.Price/del');
                Route::post('status', 'backend.http.ration.Price/status');
            });
        });

        Route::group('lock', function () {
            // 门锁品牌
            Route::group('brand', function () {
                Route::get('list', 'backend.http.lock.LockBrand/index');
                Route::post('', 'backend.http.lock.LockBrand/add');
                Route::get(':id', 'backend.http.lock.LockBrand/detail');
                Route::put(':id', 'backend.http.lock.LockBrand/edit');
                Route::delete(':id', 'backend.http.lock.LockBrand/del');
                Route::post('status', 'backend.http.lock.LockBrand/status');
            });
            // 门锁设备
            Route::group('device', function () {
                Route::get('list', 'backend.http.lock.LockDevice/index');
                // Route::post('', 'backend.http.lock.LockDevice/add');
                Route::get(':id', 'backend.http.lock.LockDevice/detail');
                Route::put(':id', 'backend.http.lock.LockDevice/edit');
                // Route::delete(':id', 'backend.http.lock.LockDevice/del');
                // Route::post('status', 'backend.http.lock.LockDevice/status');

                Route::group('third', function () {
                    Route::get(':id', 'backend.http.lock.LockDevice/deviceInfo');
                });
            });
            // 门锁管理
            Route::group('lock', function () {
                Route::get('list', 'backend.http.lock.Lock/index');
                // Route::post('', 'backend.http.lock.Lock/add');
                Route::get(':id', 'backend.http.lock.Lock/detail');
                // Route::put(':id', 'backend.http.lock.Lock/edit');
                // Route::delete(':id', 'backend.http.lock.Lock/del');
                // Route::post('status', 'backend.http.lock.Lock/status');

                Route::post('bind', 'backend.http.lock.Lock/bindLock');
                Route::post('unbind', 'backend.http.lock.Lock/unBindLock');
                Route::post('card-clear', 'backend.http.lock.Lock/cardClear');
            });
            // 门卡管理
            Route::group('card', function () {
                Route::get('list', 'backend.http.lock.LockCard/index');
                Route::post('', 'backend.http.lock.LockCard/add');
                Route::get(':id', 'backend.http.lock.LockCard/detail');
                Route::put(':id', 'backend.http.lock.LockCard/edit');
                Route::delete(':id', 'backend.http.lock.LockCard/del');
                Route::post('status', 'backend.http.lock.LockCard/status');
            });
            // 门锁日志
            Route::group('log', function () {
                Route::get('list', 'backend.http.lock.LockLog/index');
                Route::get(':id', 'backend.http.lock.LockLog/detail');
                Route::delete(':id', 'backend.http.lock.LockLog/del');
            });
        });

        // 信息预警
        Route::group('warning', function () {
            // 失信库
            Route::group('dishonesty', function () {
                Route::get('list', 'backend.http.warning.Dishonesty/index');
                Route::post('', 'backend.http.warning.Dishonesty/add');
                Route::get(':id', 'backend.http.warning.Dishonesty/detail');
                Route::put(':id', 'backend.http.warning.Dishonesty/edit');
                Route::delete(':id', 'backend.http.warning.Dishonesty/del');
                Route::post('status', 'backend.http.warning.Dishonesty/status');
            });
        });

        Route::group('lh', function () {
            Route::group('intention', function () {
                Route::get('list', 'backend.http.lh.Intention/index');
                Route::post('', 'backend.http.lh.Intention/add');
                Route::get(':id', 'backend.http.lh.Intention/detail');
                Route::put(':id', 'backend.http.lh.Intention/edit');
                Route::delete(':id', 'backend.http.lh.Intention/del');
                Route::post('status', 'backend.http.lh.Intention/status');
            });

            Route::group('family-members', function () {
                Route::get('list', 'backend.http.lh.FamilyMembers/index');
                Route::post('', 'backend.http.lh.FamilyMembers/add');
                Route::get(':id', 'backend.http.lh.FamilyMembers/detail');
                Route::put(':id', 'backend.http.lh.FamilyMembers/edit');
                Route::delete(':id', 'backend.http.lh.FamilyMembers/del');
                Route::post('status', 'backend.http.lh.FamilyMembers/status');
            });

            Route::group('information', function () {
                Route::get('list', 'backend.http.lh.ResidentInformation/index');
                Route::post('', 'backend.http.lh.ResidentInformation/add');
                Route::get(':id', 'backend.http.lh.ResidentInformation/detail');
                Route::put(':id', 'backend.http.lh.ResidentInformation/edit');
                Route::delete(':id', 'backend.http.lh.ResidentInformation/del');
                Route::post('status', 'backend.http.lh.ResidentInformation/status');
            });
        });

        // 工作流管理
        Route::group('workflow', function () {
            // region 流程控制
            Route::group('process', function () {
                Route::get('list', 'backend.http.workflow.processInstance/index');
                Route::get(':id', 'backend.http.workflow.processInstance/detail');
                Route::get('event/:id', 'backend.http.workflow.processInstance/event');

            });
            Route::group('task', function () {
                Route::get('list', 'backend.http.workflow.processTask/index');
                Route::get(':id', 'backend.http.workflow.processTask/detail');
                Route::get('view', 'backend.http.workflow.processTask/taskView');
                Route::post('execute', 'backend.http.workflow.processTask/execute');


            });
            // endregion
        });


        // 财务管理
        Route::group('finance', function () {
            Route::group('fee-items', function () {
                Route::get('list', 'backend.http.finance.FeeItems/index');
                Route::post('', 'backend.http.finance.FeeItems/add');
                Route::put(':id', 'backend.http.finance.FeeItems/edit');
                Route::delete(':id', 'backend.http.finance.FeeItems/del');
                Route::post('batchDel', 'backend.http.finance.FeeItems/batchDel');
                Route::post('status', 'backend.http.finance.FeeItems/status');
            });

            Route::group('fee-rules', function () {
                Route::get('list', 'backend.http.finance.FeeRules/index');
                Route::post('', 'backend.http.finance.FeeRules/add');
                Route::put(':id', 'backend.http.finance.FeeRules/edit');
                Route::delete(':id', 'backend.http.finance.FeeRules/del');
                Route::post('batchDel', 'backend.http.finance.FeeRules/batchDel');
                Route::post('status', 'backend.http.finance.FeeRules/status');
            });

            Route::group('fee-bill', function () {
                Route::get('list', 'backend.http.finance.FeeBill/index');
                Route::post('', 'backend.http.finance.FeeBill/add');
                Route::put(':id', 'backend.http.finance.FeeBill/edit');
                Route::delete(':id', 'backend.http.finance.FeeBill/del');
                Route::post('payment', 'backend.http.finance.FeeBill/rentPayment');
            });
        });
    })->middleware([
        BackendAuth::class,
        BackendPermission::class
    ]);
})->pattern(['id' => '\d+']);
