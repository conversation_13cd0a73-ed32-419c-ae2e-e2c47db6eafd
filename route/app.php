<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;
use app\middleware\FrontWxAuth;


Route::miss('miss/miss');

Route::group('front', function () {
    Route::group('wx', function () {
        Route::post('login', 'front.http.wx.Login/login');
        Route::get('config', 'front.http.Index/config');

        Route::get('areas-code', 'front.http.Index/getAreasCode');
        Route::get('region-code', 'front.http.Index/getRegionCode');

        Route::group('', function () {
            Route::post('getPhone', 'front.http.wx.Login/getUserPhoneNumber');
            Route::get('user', 'front.http.wx.Login/userInfo');
            Route::put('user', 'front.http.wx.Login/updateUserInfo');

            Route::group('ration', function () {
                // 配前-期次
                Route::group('period', function () {
                    Route::get('list', 'front.http.ration.Period/index');
                    Route::get('get-one', 'front.http.ration.Period/getOne');
                    Route::get('house-list', 'front.http.ration.Period/houseList');
                });
                // 配前-申请
                Route::group('apply', function () {
                    Route::get('list', 'front.http.ration.Apply/index');
                    Route::post('', 'front.http.ration.Apply/add');
                    Route::put(':id', 'front.http.ration.Apply/edit');
                    Route::get(':id', 'front.http.ration.Apply/detail');
                    Route::delete(':id', 'front.http.ration.Apply/del');

                    Route::post('check-apply', 'front.http.ration.Apply/checkApply'); // 检查申请
                    Route::put('stage/:id', 'front.http.ration.Apply/stage'); // 暂存

                    Route::post('tjsp', 'front.http.ration.Apply/tjsp'); //提交审批

                    Route::post('upload', 'front.http.ajax/upload')->append(['topic' => 'wx_ration_apply']);
                });

                Route::group('contract', function () {
                    Route::get('list', 'front.http.ration.TenantContract/index');
                    Route::get('detail', 'front.http.ration.TenantContract/detail');
                });
                Route::group('lease', function () {
                    Route::get('list', 'front.http.ration.TenantLease/index');
                    Route::get('detail', 'front.http.ration.TenantLease/detail');
                });
                Route::group('review', function () {
                    Route::get('list', 'front.http.ration.TenantReview/index');
                    Route::post('create', 'front.http.ration.TenantReview/create');
                    Route::get('detail', 'front.http.ration.TenantReview/detail');
                });

            });

            Route::group('ai', function () {
                Route::post('ask', 'front.http.ai.AI/ask');
            });

            Route::get('all-dict-data', 'front.http.system.Dict/getDictData');
            Route::group('street-code', function () {
                Route::get('list', 'front.http.system.StreetCode/index');
            });
            Route::group('management-area', function () {
                Route::get('list', 'front.http.system.ManagementArea/index');
            });
            Route::group('banner', function () {
                Route::get('list', 'front.http.system.Banner/index');
            });

            Route::group('message', function () {
                Route::get('list', 'front.http.system.AppMessage/index');
                Route::get('detail', 'front.http.system.AppMessage/detail');
                Route::get('unread', 'front.http.system.AppMessage/unreadCount');
            });

            Route::group('notice', function () {
                Route::get('list', 'front.http.system.AppNotice/index');
                Route::get('detail', 'front.http.system.AppNotice/detail');
            });

            Route::group('feedback', function () {
                Route::post('', 'front.http.system.AppFeedback/create');
            });

            Route::group('housing', function () {
                Route::group('estate', function () {
                    Route::get('list', 'front.http.housing.estate/index');
                    Route::get('detail', 'front.http.housing.estate/detail');
                });

                Route::group('apartment-layout', function () {
                    Route::get('list', 'front.http.housing.ApartmentLayout/index');
                });
            });

            Route::group('affordable-housing', function () {
                Route::group('applicants', function () {
                    Route::post('submit', 'front.http.affordable_housing.applicants/submit');
                    Route::post('submit-resident', 'front.http.affordable_housing.applicants/submitResident');
                    Route::get('resident', 'front.http.affordable_housing.applicants/getResident');
                    Route::get('query', 'front.http.affordable_housing.applicants/query');
                    Route::post('intention', 'front.http.affordable_housing.applicants/intentionCollection');
                });
            });
        })->middleware([FrontWxAuth::class]);
    });
})->pattern(['id' => '\d+']);
