<?php
/**
 * 房源导入性能测试脚本
 * 用于验证优化效果
 */

// 模拟测试数据
function generateTestData($count = 100) {
    $testData = [];
    
    for ($i = 1; $i <= $count; $i++) {
        $testData[] = [
            'area_code' => '测试区域' . (($i % 3) + 1),
            'fwxz' => '商品房',
            'fwzt' => '空置',
            'hx' => '两室一厅',
            'xqmc' => '测试小区' . (($i % 10) + 1),
            'ldmc' => '测试楼栋' . (($i % 5) + 1),
            'dy' => '单元' . (($i % 3) + 1),
            'lc' => ($i % 20) + 1,
            'xh' => $i,
            'sh' => sprintf('%02d%02d', ($i % 20) + 1, ($i % 10) + 1),
            'jzmj' => 80 + ($i % 50),
            'bz' => '测试房源' . $i,
        ];
    }
    
    return $testData;
}

// 性能测试函数
function performanceTest($recordCount) {
    echo "=== 房源导入性能测试 ===\n";
    echo "测试记录数量: {$recordCount}\n\n";
    
    // 生成测试数据
    $testData = generateTestData($recordCount);
    
    // 原始方法性能估算
    echo "原始方法性能估算:\n";
    $oldQueries = $recordCount * 6; // 每条记录6次查询
    echo "- 预估数据库查询次数: {$oldQueries}\n";
    echo "- 字典查询: " . ($recordCount * 4) . "\n";
    echo "- 小区查询: {$recordCount}\n";
    echo "- 楼栋查询: {$recordCount}\n";
    echo "- 插入操作: {$recordCount}\n\n";
    
    // 优化方法性能估算
    echo "优化方法性能估算:\n";
    $uniqueEstates = count(array_unique(array_column($testData, 'xqmc')));
    $uniqueBuildings = count(array_unique(array_map(function($item) {
        return $item['xqmc'] . '|' . $item['ldmc'];
    }, $testData)));
    
    $newQueries = 6; // 4次字典查询 + 1次小区查询 + 1次楼栋查询
    $batchInserts = ceil($recordCount / 100); // 每100条一批
    
    echo "- 总数据库查询次数: {$newQueries}\n";
    echo "- 字典查询: 4 (预加载)\n";
    echo "- 小区查询: 1 (批量查询)\n";
    echo "- 楼栋查询: 1 (批量查询)\n";
    echo "- 批量插入操作: {$batchInserts}\n";
    echo "- 唯一小区数量: {$uniqueEstates}\n";
    echo "- 唯一楼栋数量: {$uniqueBuildings}\n\n";
    
    // 性能提升计算
    echo "性能提升对比:\n";
    $queryReduction = round((1 - $newQueries / $oldQueries) * 100, 2);
    echo "- 查询次数减少: {$queryReduction}%\n";
    echo "- 从 {$oldQueries} 次减少到 {$newQueries} 次\n\n";
    
    return [
        'record_count' => $recordCount,
        'old_queries' => $oldQueries,
        'new_queries' => $newQueries,
        'query_reduction' => $queryReduction,
        'unique_estates' => $uniqueEstates,
        'unique_buildings' => $uniqueBuildings,
        'batch_inserts' => $batchInserts
    ];
}

// 运行测试
echo "房源导入性能优化测试\n";
echo "=====================\n\n";

$testCases = [100, 500, 1000, 5000];

foreach ($testCases as $count) {
    $result = performanceTest($count);
    echo "测试完成!\n";
    echo str_repeat("-", 50) . "\n\n";
}

// API 调用示例
echo "API 调用示例:\n";
echo "=============\n\n";

echo "1. 房源导入接口:\n";
echo "POST /api/backend/housing/house/import\n";
echo "{\n";
echo '    "import_info": [' . "\n";
echo '        {' . "\n";
echo '            "area_code": "测试区域1",' . "\n";
echo '            "fwxz": "商品房",' . "\n";
echo '            "fwzt": "空置",' . "\n";
echo '            "hx": "两室一厅",' . "\n";
echo '            "xqmc": "测试小区1",' . "\n";
echo '            "ldmc": "测试楼栋1",' . "\n";
echo '            "dy": "单元1",' . "\n";
echo '            "lc": 1,' . "\n";
echo '            "xh": 1,' . "\n";
echo '            "sh": "0101",' . "\n";
echo '            "jzmj": 80,' . "\n";
echo '            "bz": "测试房源1"' . "\n";
echo '        }' . "\n";
echo '    ]' . "\n";
echo "}\n\n";

echo "2. 性能测试接口:\n";
echo "POST /api/backend/housing/house-performance-test/performance-test\n";
echo "{\n";
echo '    "import_info": [...测试数据...],' . "\n";
echo '    "test_type": "both"  // both, old, new' . "\n";
echo "}\n\n";

echo "3. 生成测试数据接口:\n";
echo "GET /api/backend/housing/house-performance-test/generate-test-data?count=1000\n\n";

echo "优化要点总结:\n";
echo "=============\n";
echo "1. 预加载字典数据，避免重复查询\n";
echo "2. 批量查询小区和楼栋信息\n";
echo "3. 使用 insertAll 进行批量插入\n";
echo "4. 优化数据验证流程\n";
echo "5. 减少内存使用和数据库连接\n\n";

echo "注意事项:\n";
echo "=========\n";
echo "1. 确保服务器有足够内存处理大批量数据\n";
echo "2. 数据库需要有适当的索引支持批量查询\n";
echo "3. 建议在小区名称和楼栋名称字段上建立索引\n";
echo "4. 对于超大批量数据，考虑使用异步队列处理\n";
?>
